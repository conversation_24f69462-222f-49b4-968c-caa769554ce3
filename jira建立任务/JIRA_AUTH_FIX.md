# JIRA认证方式修复和任务创建流程优化

## 修复内容

### 1. JIRA API认证方式修复

**问题**: 之前使用的是Basic Auth认证方式，与实际JIRA API要求不符。

**修复**: 改为使用Bearer Token认证方式。

#### 修复前
```python
from requests.auth import HTTPBasicAuth
auth = HTTPBasicAuth(config['username'], config['token'])
response = self.session.post(url, json=data, auth=auth)
```

#### 修复后
```python
headers = {
    'Authorization': f"Bearer {config['token']}",
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}
response = self.session.post(url, json=data, headers=headers)
```

### 2. API端点修复

**修复**: 统一使用 `/rest/api/latest/issue` 端点，与实际JIRA API保持一致。

#### 修复前
```python
create_url = f"{jira_url}/rest/api/2/issue"
```

#### 修复后
```python
create_url = f"{jira_url}/rest/api/latest/issue"
```

### 3. 任务创建流程优化

**问题**: 之前子任务创建时，即使主任务创建失败，仍会尝试创建子任务，导致不必要的API调用。

**修复**: 在子任务创建前预先检查主任务创建状态，如果主任务创建失败，直接将相关子任务标记为失败。

#### 修复后的逻辑
```python
def _create_sub_tasks(self, config, sub_tasks_list, main_task_keys, results):
    # 预先检查并标记失败的子任务（主任务创建失败的情况）
    valid_sub_tasks = []
    failed_sub_tasks = []
    
    for sub_task_info in sub_tasks_list:
        main_task_name = sub_task_info['main_task']
        parent_key = main_task_keys.get(main_task_name)
        
        if not parent_key:
            # 主任务创建失败，子任务直接标记为失败
            error_msg = f"主任务 '{main_task_name}' 创建失败，子任务无法创建"
            # 记录失败结果...
            failed_sub_tasks.append(sub_task_info)
        else:
            valid_sub_tasks.append(sub_task_info)
    
    # 只创建有效的子任务
    for sub_task_info in valid_sub_tasks:
        # 创建子任务...
```

## 涉及的文件修改

### 1. `app/services/jira_service.py`

**修改内容**:
- 移除 `HTTPBasicAuth` 导入
- 修改所有JIRA API调用的认证方式
- 优化子任务创建流程
- 统一API端点为 `/rest/api/latest/issue`

**影响的方法**:
- `create_jira_issue()` - 原有的创建方法
- `test_jira_connection()` - 连接测试
- `_create_jira_issue_api()` - 通用创建API
- `_find_sprint_id()` - Sprint查询
- `_link_task_to_sprint()` - Sprint关联
- `_link_demand_to_task()` - 需求关联
- `_create_sub_tasks()` - 子任务创建

### 2. `test_hierarchy_creation.py`

**修改内容**:
- 更新测试配置，使用实际的Bearer Token
- 使用实际的项目Key和用户名

## 认证配置示例

### 前端配置
```json
{
  "jira_config": {
    "environment": "test",
    "username": "lidezheng",
    "token": "OTA3ODQ3MzI3MjgwOvqvwJR5zufAp/ibk9NkjWqAaTw+",
    "project_key": "JGKEZH",
    "sprint": "INST2025-sprint10",
    "test_assignee": "zhouqishu"
  }
}
```

### curl示例
```bash
curl --request POST \
  --url http://jirauat.gf.com.cn/rest/api/latest/issue \
  --header 'Authorization: Bearer OTA3ODQ3MzI3MjgwOvqvwJR5zufAp/ibk9NkjWqAaTw+' \
  --header 'Content-Type: application/json' \
  --header 'Accept: application/json' \
  --data '{
    "fields": {
        "summary": "试一下",
        "priority": {"id": "3"},
        "issuetype": {"id": "11007"},
        "project": {"key": "JGKEZH"},
        "reporter": {"name": "lidezheng"},
        "assignee": {"name": "linwenjie"},
        "customfield_13103": {"name": "guanyuan"},
        "customfield_11305": {"name": "zhouqishu"}
    }
}'
```

## 优化效果

### 1. 认证成功率提升
- 使用正确的Bearer Token认证方式
- 符合JIRA API标准

### 2. 错误处理优化
- 主任务创建失败时，相关子任务立即标记为失败
- 减少不必要的API调用
- 提供更清晰的错误信息

### 3. 日志记录改进
- 详细记录预检查结果
- 区分有效子任务和失败子任务
- 提供准确的统计信息

## 测试验证

### 1. 连接测试
```python
# 测试JIRA连接
result = jira_service.test_jira_connection(config)
```

### 2. 任务创建测试
```python
# 测试层次结构任务创建
result = jira_service.batch_create_issues_with_hierarchy(config, tasks)
```

### 3. 日志查看
```bash
# 查看JIRA API日志
tail -f logs/jira_api_$(date +%Y%m%d).log
```

## 注意事项

### 1. Token安全
- Bearer Token是敏感信息，需要妥善保管
- 不要在日志中记录完整的Token
- 建议定期更换Token

### 2. API限制
- 注意JIRA API的调用频率限制
- 合理设置超时时间
- 处理API返回的错误码

### 3. 环境配置
- 确保测试环境和生产环境的配置正确
- 验证项目Key和用户权限
- 测试自定义字段的可用性

## 兼容性

### 支持的JIRA版本
- JIRA 7.x 及以上版本
- 支持REST API v2 和 latest

### 支持的认证方式
- Bearer Token (推荐)
- 兼容旧版Basic Auth (如需要)

### 支持的任务类型
- 主任务: 迭代功能 (ID: 11007)
- 子任务: 前端开发 (ID: 11012)、后端开发 (ID: 11013)、数据开发 (ID: 11014)
