#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: logging.py
描述: 日志配置文件

更新日志:
2024-01-15 - 创建日志配置模块
"""

import os
import logging
import logging.handlers
from datetime import datetime

def setup_logging():
    """设置日志配置"""
    
    # 创建logs目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 日志级别
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器 - 按日期轮转
    log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_file,
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)  # 文件记录所有级别的日志
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # JIRA API专用日志器
    jira_logger = logging.getLogger('jira_api')
    jira_logger.setLevel(logging.DEBUG)
    
    # JIRA API专用文件处理器
    jira_log_file = os.path.join(log_dir, f"jira_api_{datetime.now().strftime('%Y%m%d')}.log")
    jira_file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=jira_log_file,
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    jira_file_handler.setLevel(logging.DEBUG)
    
    # JIRA API专用格式器 - 更详细的格式
    jira_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    jira_file_handler.setFormatter(jira_formatter)
    jira_logger.addHandler(jira_file_handler)
    
    # 允许传播到根日志器，这样控制台也能看到JIRA日志
    jira_logger.propagate = True
    
    logging.info("日志系统初始化完成")
    logging.info(f"日志级别: {log_level}")
    logging.info(f"应用日志文件: {log_file}")
    logging.info(f"JIRA API日志文件: {jira_log_file}")

def get_jira_logger():
    """获取JIRA专用日志器"""
    return logging.getLogger('jira_api')
