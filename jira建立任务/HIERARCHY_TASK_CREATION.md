# JIRA层次结构任务创建功能说明

## 概述

本项目已实现了按照主任务-子任务层次结构的JIRA任务批量创建功能，严格按照以下步骤执行：

1. **创建所有主任务**，记录主任务的Jira key
2. **按照表单的Sprint查找Sprint id**，更新、关联到任务
3. **将橙卡（需求）关联到主任务**
4. **创建主任务下的子任务**

## 功能特性

### 1. 智能任务层次分析

- 自动分析Excel数据中的主任务和子任务关系
- 支持空主任务字段的自动填充（继承上一行的主任务）
- 去重主任务，确保每个主任务只创建一次

### 2. 主任务创建

**任务类型**: 迭代功能 (ID: 11007)

**包含字段**:
- 项目Key
- 任务标题（主任务名称）
- 任务描述（包含橙卡、模块、工作量信息）
- 优先级（中等）
- 负责人（自动转换为OA账号）
- 开发责任人（自定义字段）
- 测试责任人（固定为zhouqishu）

### 3. Sprint关联

- 使用Greenhopper API查询Sprint ID
- 支持从suggestions和allMatches中查找Sprint
- 自动关联所有主任务到指定Sprint

### 4. 需求关联

- 使用Issue Links API建立需求关联
- 关联类型：需求关联
- 支持多个主任务关联到同一个需求

### 5. 子任务创建

**支持的任务类型**:
- UI → 前端开发子任务 (ID: 11012)
- API → 后端开发子任务 (ID: 11013)
- 数据 → 数据开发子任务 (ID: 11014)
- 测试 → 测试开发子任务 (ID: 11015)

**包含字段**:
- 项目Key
- 任务标题（子任务名称）
- 任务类型（根据Excel中的任务类型自动映射）
- 优先级（中等）
- 父任务（关联到对应的主任务）
- 负责人（自动转换为OA账号）
- 预估耗时（以天为单位，如：0.5d、1d、2d）

## 数据格式要求

### Excel数据格式

| 字段名 | 说明 | 示例 | 必填 |
|--------|------|------|------|
| demand | 橙卡（需求JIRA Key） | JGKEZH-23876 | 否 |
| module | 模块名称 | 《MCP追加第四、五大点》 | 否 |
| main_task | 主任务名称 | MCP优化 | 是 |
| sub_task | 子任务名称 | web端 应用文案全部替换为fundx应用 | 是 |
| assignee | 负责人中文姓名 | 关远 | 是 |
| workload | 工作量（天） | 0.3 | 否 |
| task_type | 任务类型 | UI/API/数据/测试 | 是 |

### 前端传参格式

```json
{
  "jira_config": {
    "environment": "test",
    "username": "user.name",
    "token": "api_token",
    "project_key": "PROJECT",
    "sprint": "INST2025-sprint10"
  },
  "tasks": [
    {
      "demand": "JGKEZH-23876",
      "module": "《MCP追加第四、五大点》",
      "main_task": "MCP优化",
      "sub_task": "web端 应用文案全部替换为fundx应用",
      "assignee": "关远",
      "workload": "0.3",
      "task_type": "UI"
    },
    {
      "demand": "",
      "module": "",
      "main_task": "",
      "sub_task": "cms端 应用文案全部替换为fundx应用",
      "assignee": "关远",
      "workload": "0.1",
      "task_type": "UI"
    }
  ]
}
```

## API接口

### 批量创建任务

**接口**: `POST /api/v1/create-jira-tasks`

**响应格式**:
```json
{
  "success": true,
  "data": {
    "success": [
      {
        "success": true,
        "type": "main_task",
        "key": "PROJECT-123",
        "title": "MCP优化",
        "assignee": "关远",
        "url": "http://jira.example.com/browse/PROJECT-123"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "PROJECT-124",
        "title": "web端 应用文案全部替换为fundx应用",
        "parent_key": "PROJECT-123",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jira.example.com/browse/PROJECT-124"
      }
    ],
    "failed": [
      {
        "success": false,
        "type": "sprint_link",
        "title": "Sprint关联",
        "error": "未找到Sprint: INST2025-sprint10"
      }
    ],
    "summary": {
      "total": 8,
      "success_count": 7,
      "failed_count": 1,
      "main_tasks_created": 2,
      "sub_tasks_created": 5,
      "sprint_linked": 2,
      "demands_linked": 2
    }
  }
}
```

## 执行流程

### 1. 任务结构分析

```
输入任务列表 → 分析主任务和子任务关系 → 生成主任务映射表和子任务列表
```

### 2. 主任务创建

```
遍历主任务映射表 → 构建主任务数据 → 调用JIRA API创建 → 记录JIRA Key
```

### 3. Sprint关联

```
查询Sprint ID → 遍历主任务Key → 更新任务Sprint字段 → 记录关联结果
```

### 4. 需求关联

```
遍历主任务 → 检查需求字段 → 创建Issue Link → 记录关联结果
```

### 5. 子任务创建

```
遍历子任务列表 → 查找父任务Key → 构建子任务数据 → 调用JIRA API创建
```

## 错误处理

### 常见错误类型

1. **主任务创建失败**: 权限不足、字段验证失败
2. **Sprint关联失败**: Sprint不存在、权限不足
3. **需求关联失败**: 需求不存在、权限不足
4. **子任务创建失败**: 父任务不存在、权限不足

### 错误恢复策略

- 主任务创建失败时，跳过相关的子任务创建
- Sprint关联失败时，不影响任务创建
- 需求关联失败时，不影响任务创建
- 子任务创建失败时，记录错误但继续创建其他子任务

## 日志记录

所有操作都会记录详细的日志信息：

- **请求日志**: API调用的URL、方法、参数、请求体
- **响应日志**: 状态码、响应头、响应体
- **业务日志**: 任务创建进度、关联结果、错误信息
- **统计日志**: 各阶段的成功/失败统计

## 测试方法

### 1. 单元测试

```bash
python test_hierarchy_creation.py
```

### 2. 集成测试

```bash
# 启动服务
python run.py

# 使用Postman或curl测试API
curl -X POST "http://localhost:8000/api/v1/create-jira-tasks" \
  -H "Content-Type: application/json" \
  -d @test_data.json
```

### 3. 日志查看

```bash
# 查看应用日志
tail -f logs/app_$(date +%Y%m%d).log

# 查看JIRA API日志
tail -f logs/jira_api_$(date +%Y%m%d).log
```

## 配置说明

### 环境配置

在 `config/jira.py` 中配置不同环境的JIRA地址：

```python
JIRA_ENVIRONMENTS = {
    'test': 'http://jirauat.gf.com.cn',
    'production': 'http://jira.gf.com.cn'
}
```

### 任务类型映射

在 `JiraService` 中配置任务类型映射：

```python
task_type_mapping = {
    'UI': '11012',      # 前端开发子任务
    'API': '11013',     # 后端开发子任务
    '测试': '11014'      # 数据开发子任务
}
```

### 自定义字段

根据实际JIRA配置调整自定义字段ID：

- `customfield_13103`: 开发责任人
- `customfield_11305`: 测试责任人
- `customfield_10005`: Sprint字段
