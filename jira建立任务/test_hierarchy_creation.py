#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: test_hierarchy_creation.py
描述: 测试JIRA层次结构任务创建功能

使用方法:
python test_hierarchy_creation.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.logging import setup_logging
from app.services.jira_service import JiraService

def test_hierarchy_creation():
    """测试层次结构任务创建"""
    print("=" * 80)
    print("测试JIRA层次结构任务创建功能")
    print("=" * 80)
    
    # 初始化日志系统
    setup_logging()
    
    # 创建JIRA服务实例
    jira_service = JiraService()
    
    # 测试配置
    test_config = {
        'environment': 'test',
        'username': 'test_user',
        'token': 'test_token',
        'project_key': 'TEST',
        'sprint': 'INST2025-sprint10'
    }
    
    # 测试任务数据 - 模拟Excel解析后的数据结构
    test_tasks = [
        # 第一个主任务及其子任务
        {
            'demand': 'JGKEZH-23876',
            'module': '《MCP追加第四、五大点》',
            'main_task': 'MCP优化',
            'sub_task': 'web端 应用文案全部替换为fundx应用',
            'assignee': '关远',
            'workload': '0.3',
            'task_type': 'UI'
        },
        {
            'demand': '',  # 空的需求，会继承上一行
            'module': '',  # 空的模块，会继承上一行
            'main_task': '',  # 空的主任务，会继承上一行
            'sub_task': 'cms端 应用文案全部替换为fundx应用',
            'assignee': '关远',
            'workload': '0.1',
            'task_type': 'UI'
        },
        {
            'demand': '',
            'module': '',
            'main_task': '',
            'sub_task': 'MCP优化测试',
            'assignee': '陈贺',
            'workload': '1',
            'task_type': '测试'
        },
        {
            'demand': '',
            'module': '',
            'main_task': '',
            'sub_task': 'CMS自动生成【MCP介绍】',
            'assignee': '关远',
            'workload': '1',
            'task_type': 'UI'
        },
        {
            'demand': '',
            'module': '',
            'main_task': '',
            'sub_task': 'mcp服务可用工具列表接口',
            'assignee': '林文杰',
            'workload': '1',
            'task_type': 'API'
        },
        
        # 第二个主任务及其子任务
        {
            'demand': 'JGKEZH-24150',
            'module': '用户管理模块',
            'main_task': '用户权限优化',
            'sub_task': '用户角色管理界面优化',
            'assignee': '张三',
            'workload': '2',
            'task_type': 'UI'
        },
        {
            'demand': '',
            'module': '',
            'main_task': '',
            'sub_task': '权限验证API接口',
            'assignee': '李四',
            'workload': '1.5',
            'task_type': 'API'
        },
        {
            'demand': '',
            'module': '',
            'main_task': '',
            'sub_task': '权限功能测试',
            'assignee': '王五',
            'workload': '1',
            'task_type': '测试'
        }
    ]
    
    # 预处理任务数据，填充空的主任务字段
    processed_tasks = preprocess_tasks(test_tasks)
    
    print("预处理后的任务数据:")
    for i, task in enumerate(processed_tasks):
        print(f"  {i+1}. 主任务: {task.get('main_task', 'N/A')}")
        print(f"     子任务: {task.get('sub_task', 'N/A')}")
        print(f"     负责人: {task.get('assignee', 'N/A')}")
        print(f"     任务类型: {task.get('task_type', 'N/A')}")
        print(f"     需求: {task.get('demand', 'N/A')}")
        print()
    
    print("正在测试JIRA层次结构任务创建...")
    print("请查看logs/jira_api_*.log文件查看详细的API调用日志")
    
    # 执行层次结构批量创建
    result = jira_service.batch_create_issues_with_hierarchy(test_config, processed_tasks)
    
    print("\n" + "=" * 80)
    print("任务创建结果:")
    print("=" * 80)
    print(f"总任务数: {result.get('total', 0)}")
    print(f"主任务创建: {result.get('main_tasks_created', 0)} 个")
    print(f"子任务创建: {result.get('sub_tasks_created', 0)} 个")
    print(f"Sprint关联: {result.get('sprint_linked', 0)} 个")
    print(f"需求关联: {result.get('demands_linked', 0)} 个")
    print(f"成功任务: {len(result.get('success_tasks', []))} 个")
    print(f"失败任务: {len(result.get('failed_tasks', []))} 个")
    
    if result.get('success_tasks'):
        print("\n成功创建的任务:")
        for task in result['success_tasks']:
            print(f"  - {task.get('type', 'unknown')}: {task.get('key', 'N/A')} - {task.get('title', 'N/A')}")
    
    if result.get('failed_tasks'):
        print("\n失败的任务:")
        for task in result['failed_tasks']:
            print(f"  - {task.get('type', 'unknown')}: {task.get('title', 'N/A')} - {task.get('error', 'N/A')}")
    
    print("\n请检查以下日志文件:")
    print("- logs/app_*.log (应用日志)")
    print("- logs/jira_api_*.log (JIRA API专用日志)")

def preprocess_tasks(tasks):
    """预处理任务数据，填充空的主任务字段"""
    processed_tasks = []
    current_main_task = ""
    current_demand = ""
    current_module = ""
    
    for task in tasks:
        # 复制任务数据
        processed_task = task.copy()
        
        # 填充主任务字段
        if task.get('main_task', '').strip():
            current_main_task = task['main_task'].strip()
        processed_task['main_task'] = current_main_task
        
        # 填充需求字段
        if task.get('demand', '').strip():
            current_demand = task['demand'].strip()
        processed_task['demand'] = current_demand
        
        # 填充模块字段
        if task.get('module', '').strip():
            current_module = task['module'].strip()
        processed_task['module'] = current_module
        
        processed_tasks.append(processed_task)
    
    return processed_tasks

def main():
    """主函数"""
    print("JIRA层次结构任务创建测试工具")
    print("=" * 80)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试层次结构任务创建")
        print("2. 退出")
        
        choice = input("\n请输入选择 (1-2): ").strip()
        
        if choice == '1':
            test_hierarchy_creation()
        elif choice == '2':
            print("退出测试工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
