#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: run.py
描述: 项目启动脚本

使用方法:
python run.py

更新日志:
2024-01-15 20:40 - 创建了项目启动脚本
"""

import uvicorn
import os
import sys

# 将项目根目录添加到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    # 检查是否为开发环境
    is_development = os.getenv("ENVIRONMENT", "development") == "development"
    
    # 启动配置
    config = {
        "app": "app.main:app",
        "host": "0.0.0.0",
        "port": 8000,
        "reload": is_development,
        "log_level": "info" if is_development else "warning",
        "access_log": is_development
    }
    
    print("=" * 60)
    print("🚀 Jira任务批量创建工具")
    print("=" * 60)
    print(f"📍 访问地址: http://localhost:{config['port']}")
    print(f"📊 API文档: http://localhost:{config['port']}/docs")
    print(f"🔧 环境模式: {'开发' if is_development else '生产'}")
    print("=" * 60)
    
    # 启动服务
    uvicorn.run(**config) 