---
description: 
globs: 
alwaysApply: false
---
# 代码变更日志要求

## 规则说明

每次编辑代码文件后，必须在项目根目录的 [update.md](mdc:update.md) 文件中添加更新日志记录。

## 更新日志文件要求

### 1. 文件位置
- 更新日志统一记录在项目根目录的 `update.md` 文件中
- 如果文件不存在，需要先创建该文件

### 2. 格式规范
```markdown
# 项目更新日志

## 2024-01-15

### 14:30 - jira_service.py
- 添加了Sprint关联功能
- 实现了自动查询Sprint ID的逻辑

### 09:15 - database/connection.py
- 修复了数据库连接超时问题
- 增加了连接池配置

## 2024-01-14

### 16:45 - services/excel_parser.py
- 优化了Excel文件解析性能
- 添加了批量处理功能
```

### 3. 内容要求
- **获取当前时间**: 可以使用get_current_time
- **日期分组**：按日期分组，使用 `## YYYY-MM-DD` 格式
- **时间和文件**：使用 `### HH:MM - 文件路径` 格式
- **变更描述**：使用中文，用列表形式描述具体变更内容
- **变更类型**：可包含但不限于：
  - 添加功能
  - 修复bug
  - 性能优化
  - 代码重构
  - 配置调整
  - 文档更新

### 4. update.md 文件完整示例

```markdown
# 项目更新日志

## 2024-01-15

### 16:30 - app/services/jira_service.py
- 添加了Sprint关联功能
- 实现了Greenhopper API集成
- 优化了错误处理逻辑

### 15:45 - app/static/js/app.js
- 添加了文件上传进度显示
- 实现了实时状态更新
- 修复了表格排序问题

### 14:20 - app/templates/index.html
- 添加了Bootstrap样式
- 实现了响应式设计
- 优化了用户交互体验

### 11:30 - requirements.txt
- 添加了python-jira依赖
- 更新了pandas版本到最新稳定版

## 2024-01-14

### 17:00 - app/services/excel_parser.py
- 实现了Excel文件解析功能
- 添加了数据验证逻辑
- 支持多种文件格式

### 14:15 - app/database/connection.py
- 创建了数据库连接管理模块
- 实现了连接池功能
- 添加了重连机制

### 10:30 - app/main.py
- 创建了FastAPI应用主入口
- 配置了路由和中间件
- 添加了静态文件服务
```

## 执行要求

1. **每次编辑必须更新**：无论变更大小，都必须在 [update.md](mdc:update.md) 文件中添加更新日志条目
2. **时间准确性**：使用实际编辑时间，精确到分钟
3. **文件路径**：使用相对于项目根目录的文件路径
4. **描述清晰**：变更描述要让其他开发者能够快速理解本次变更的目的和内容
5. **中文要求**：所有更新日志必须使用中文书写
6. **格式一致**：严格按照规定格式书写，保持日志的一致性
7. **按时间倒序**：最新的变更记录在最前面

## 操作流程

1. 编辑代码文件
2. 保存文件
3. 立即打开项目根目录的 `update.md` 文件
4. 在对应日期下添加新的变更记录
5. 如果是新的日期，创建新的日期分组
6. 保存 `update.md` 文件

## 注意事项

- 如果 `update.md` 文件不存在，需要先创建该文件
- 如果是新创建的文件，应该描述文件的创建目的和主要功能
- 如果是重大重构，应该详细描述重构的原因和影响范围
- 临时调试代码的添加和删除也需要记录
- 配置文件的修改要特别注明修改的配置项和原因
- 多个文件同时修改时，每个文件都要单独记录
- 同一个文件在同一时间的多次小修改可以合并为一条记录
