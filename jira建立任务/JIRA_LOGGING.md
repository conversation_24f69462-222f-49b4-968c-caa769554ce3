# JIRA接口调用日志记录说明

## 概述

本项目已经增强了JIRA接口调用的日志记录功能，可以详细记录所有JIRA API的请求和响应信息，便于问题排查和调试。

## 日志文件位置

日志文件存储在 `logs/` 目录下：

- `app_YYYYMMDD.log` - 应用主日志文件
- `jira_api_YYYYMMDD.log` - JIRA API专用日志文件

## 日志记录内容

### JIRA API请求日志

每次调用JIRA API时，会记录以下信息：

```
================================================================================
JIRA API 请求详情:
请求URL: https://jira.example.com/rest/api/2/issue
请求方法: POST
认证用户: username
请求头: {'Content-Type': 'application/json', 'Accept': 'application/json'}
请求体 (JSON):
{
  "fields": {
    "project": {
      "key": "PROJECT"
    },
    "summary": "任务标题",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [...]
    },
    "issuetype": {
      "name": "Task"
    },
    "assignee": {
      "name": "user.oa"
    }
  }
}
================================================================================
```

### JIRA API响应日志

每次收到JIRA API响应时，会记录以下信息：

```
JIRA API 响应详情:
响应状态码: 201
响应头: {'Content-Type': 'application/json', ...}
响应体:
{
  "id": "12345",
  "key": "PROJECT-123",
  "self": "https://jira.example.com/rest/api/2/issue/12345"
}
================================================================================
```

### 批量任务创建日志

批量创建任务时，会记录详细的统计信息：

```
================================================================================
JIRA 批量任务创建开始:
任务数量: 5
环境: production
项目: PROJECT
用户: username
Sprint: Sprint 1
任务列表:
  1. 前端开发任务 - 张三
  2. 后端开发任务 - 李四
  3. 测试任务 - 王五
  ...
================================================================================
```

### 错误日志

当API调用失败时，会记录详细的错误信息：

```
JIRA API 调用失败:
状态码: 400
错误消息: JIRA API错误: 400 - Field 'assignee' cannot be set
详细错误信息:
{
  "errorMessages": [],
  "errors": {
    "assignee": "User 'invalid_user' cannot be assigned issues."
  }
}
```

## 日志配置

### 日志级别

可以通过环境变量 `LOG_LEVEL` 设置日志级别：

```bash
export LOG_LEVEL=DEBUG  # 记录所有日志
export LOG_LEVEL=INFO   # 记录INFO及以上级别日志（默认）
export LOG_LEVEL=ERROR  # 只记录错误日志
```

### 日志轮转

- 日志文件按天轮转
- 保留最近30天的日志文件
- 自动压缩旧日志文件

## 使用示例

### 1. 启动应用时查看日志

```bash
# 启动应用
python run.py

# 实时查看应用日志
tail -f logs/app_$(date +%Y%m%d).log

# 实时查看JIRA API日志
tail -f logs/jira_api_$(date +%Y%m%d).log
```

### 2. 测试JIRA接口日志记录

```bash
# 运行测试脚本
python test_jira_logging.py
```

### 3. 查看特定时间的日志

```bash
# 查看今天的JIRA API日志
cat logs/jira_api_$(date +%Y%m%d).log

# 搜索特定错误
grep -n "错误" logs/jira_api_*.log

# 搜索特定任务的创建记录
grep -n "PROJECT-123" logs/jira_api_*.log
```

## 故障排查指南

### 1. 连接问题

如果JIRA连接失败，检查日志中的：
- 请求URL是否正确
- 认证信息是否有效
- 网络连接是否正常

### 2. 任务创建失败

如果任务创建失败，检查日志中的：
- 请求体格式是否正确
- 必填字段是否缺失
- 用户权限是否足够
- 项目配置是否正确

### 3. 性能问题

如果API调用缓慢，检查日志中的：
- 请求响应时间
- 并发请求数量
- 网络延迟情况

## 安全注意事项

- 日志文件中不会记录密码和token的完整内容
- 敏感信息会被标记为 `***` 
- 建议定期清理旧日志文件
- 确保日志文件的访问权限设置正确

## 日志分析工具

可以使用以下工具分析日志：

```bash
# 统计API调用次数
grep -c "JIRA API 请求详情" logs/jira_api_*.log

# 统计成功/失败次数
grep -c "响应状态码: 201" logs/jira_api_*.log
grep -c "JIRA API 调用失败" logs/jira_api_*.log

# 分析最常见的错误
grep "错误消息" logs/jira_api_*.log | sort | uniq -c | sort -nr
```
