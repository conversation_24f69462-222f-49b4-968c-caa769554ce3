#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: test_retry_functionality.py
描述: 测试重试功能的数据结构

使用方法:
python test_retry_functionality.py
"""

import json

def test_failed_task_structure():
    """测试失败任务的数据结构"""
    
    print("=" * 80)
    print("测试失败任务数据结构")
    print("=" * 80)
    
    # 模拟失败的主任务
    failed_main_task = {
        "success": False,
        "type": "main_task",
        "title": "MCP优化",
        "assignee": "关远",
        "task_type": "主任务",
        "error": "创建主任务失败: JIRA API错误: 502",
        # 完整的原始任务信息
        "demand": "JGKEZH-23876",
        "module": "《MCP追加第四、五大点》",
        "main_task": "MCP优化",
        "sub_task": "",
        "workload": "0.3"
    }
    
    # 模拟失败的子任务
    failed_sub_task = {
        "success": False,
        "type": "sub_task",
        "title": "web端 应用文案全部替换为fundx应用",
        "main_task": "MCP优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'MCP优化' 创建失败，子任务无法创建",
        # 完整的原始任务信息
        "demand": "JGKEZH-23876",
        "module": "《MCP追加第四、五大点》",
        "sub_task": "web端 应用文案全部替换为fundx应用",
        "workload": "0.3"
    }
    
    failed_tasks = [failed_main_task, failed_sub_task]
    
    print("失败任务原始数据:")
    print(json.dumps(failed_tasks, indent=2, ensure_ascii=False))
    
    print("\n" + "=" * 80)
    print("转换为重试格式")
    print("=" * 80)
    
    # 转换为重试格式
    retry_tasks = []
    for task in failed_tasks:
        if task['type'] in ['main_task', 'sub_task']:
            retry_task = {
                'demand': task.get('demand', ''),
                'module': task.get('module', ''),
                'main_task': task.get('main_task', task['title']),
                'sub_task': task.get('sub_task', '' if task['type'] == 'main_task' else task['title']),
                'assignee': task.get('assignee', ''),
                'workload': task.get('workload', ''),
                'task_type': 'UI' if task.get('task_type') == '主任务' else task.get('task_type', '')
            }
            retry_tasks.append(retry_task)
    
    print("重试任务数据:")
    print(json.dumps(retry_tasks, indent=2, ensure_ascii=False))
    
    print("\n" + "=" * 80)
    print("验证数据完整性")
    print("=" * 80)
    
    for i, task in enumerate(retry_tasks):
        print(f"任务 {i+1}:")
        print(f"  需求: {task['demand']}")
        print(f"  模块: {task['module']}")
        print(f"  主任务: {task['main_task']}")
        print(f"  子任务: {task['sub_task']}")
        print(f"  负责人: {task['assignee']}")
        print(f"  工作量: {task['workload']}")
        print(f"  任务类型: {task['task_type']}")
        print()
    
    return retry_tasks

def test_single_task_retry():
    """测试单个任务重试"""
    
    print("=" * 80)
    print("测试单个任务重试")
    print("=" * 80)
    
    # 模拟单个失败任务
    single_failed_task = {
        "success": False,
        "type": "sub_task",
        "title": "cms端 应用文案全部替换为fundx应用",
        "main_task": "MCP优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'MCP优化' 创建失败，子任务无法创建",
        "demand": "JGKEZH-23876",
        "module": "《MCP追加第四、五大点》",
        "sub_task": "cms端 应用文案全部替换为fundx应用",
        "workload": "0.1"
    }
    
    print("单个失败任务:")
    print(json.dumps(single_failed_task, indent=2, ensure_ascii=False))
    
    # 转换为重试格式
    retry_task = {
        'demand': single_failed_task.get('demand', ''),
        'module': single_failed_task.get('module', ''),
        'main_task': single_failed_task.get('main_task', single_failed_task['title']),
        'sub_task': single_failed_task.get('sub_task', single_failed_task['title'] if single_failed_task['type'] != 'main_task' else ''),
        'assignee': single_failed_task.get('assignee', ''),
        'workload': single_failed_task.get('workload', ''),
        'task_type': 'UI' if single_failed_task.get('task_type') == '主任务' else single_failed_task.get('task_type', '')
    }
    
    print("\n转换后的重试任务:")
    print(json.dumps([retry_task], indent=2, ensure_ascii=False))
    
    return [retry_task]

def test_mixed_failed_tasks():
    """测试混合失败任务（包含不可重试的任务）"""
    
    print("=" * 80)
    print("测试混合失败任务")
    print("=" * 80)
    
    mixed_failed_tasks = [
        {
            "success": False,
            "type": "main_task",
            "title": "用户权限优化",
            "assignee": "张三",
            "task_type": "主任务",
            "error": "创建主任务失败: JIRA API错误: 502",
            "demand": "JGKEZH-24150",
            "module": "用户管理模块",
            "main_task": "用户权限优化",
            "sub_task": "",
            "workload": "2"
        },
        {
            "success": False,
            "type": "sprint_link",
            "title": "Sprint关联",
            "error": "未找到Sprint: INST2025-sprint10"
        },
        {
            "success": False,
            "type": "sub_task",
            "title": "权限验证API接口",
            "main_task": "用户权限优化",
            "assignee": "李四",
            "task_type": "API",
            "error": "主任务创建失败，子任务无法创建",
            "demand": "JGKEZH-24150",
            "module": "用户管理模块",
            "sub_task": "权限验证API接口",
            "workload": "1.5"
        }
    ]
    
    print("混合失败任务:")
    print(json.dumps(mixed_failed_tasks, indent=2, ensure_ascii=False))
    
    # 过滤并转换可重试的任务
    retry_tasks = []
    for task in mixed_failed_tasks:
        if task['type'] in ['main_task', 'sub_task']:
            retry_task = {
                'demand': task.get('demand', ''),
                'module': task.get('module', ''),
                'main_task': task.get('main_task', task['title']),
                'sub_task': task.get('sub_task', '' if task['type'] == 'main_task' else task['title']),
                'assignee': task.get('assignee', ''),
                'workload': task.get('workload', ''),
                'task_type': 'UI' if task.get('task_type') == '主任务' else task.get('task_type', '')
            }
            retry_tasks.append(retry_task)
    
    print(f"\n可重试任务数量: {len(retry_tasks)}")
    print("可重试任务:")
    print(json.dumps(retry_tasks, indent=2, ensure_ascii=False))
    
    return retry_tasks

def main():
    """主函数"""
    print("重试功能数据结构测试工具")
    print("=" * 80)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试失败任务数据结构")
        print("2. 测试单个任务重试")
        print("3. 测试混合失败任务")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            test_failed_task_structure()
        elif choice == '2':
            test_single_task_retry()
        elif choice == '3':
            test_mixed_failed_tasks()
        elif choice == '4':
            print("退出测试工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
