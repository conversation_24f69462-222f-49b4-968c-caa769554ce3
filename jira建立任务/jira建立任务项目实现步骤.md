# Jira 任务批量创建工具 - 项目实现方案

## 1. 项目概述

本项目旨在开发一个 Web 应用，允许用户通过上传一个标准格式的 Excel 文件，来批量创建 Jira 任务。系统后端将解析该文件，并根据文件内容调用 Jira API 自动创建相应的任务和子任务，并指派给对应的负责人。

该方案将解决手动创建大量相似 Jira 任务时效率低下、易出错的问题，特别适用于项目启动、需求分解、周期性任务分配等场景。

**核心流程:**

1.  **用户:** 在 Web 界面上传 Excel 模板文件。
2.  **系统:** 接收文件，校验格式，并逐行解析任务数据，返回任务列表供用户预览。
3.  **用户:** 确认任务列表无误后，填写 Jira 配置信息（服务器地址、认证信息、项目配置等）。
4.  **系统:** 接收任务列表和 Jira 配置，连接到 Jira 服务，为每一行数据创建一个 Jira Issue。
5.  **系统:** 向用户反馈创建结果，包括成功和失败的任务详情。

## 2. 技术选型

为了快速搭建和试用，我们选择**纯Python全栈方案**，避免复杂的前后端分离部署：

### 2.1. 推荐方案：纯Python全栈（最快速）

*   **Web框架:** **FastAPI + Jinja2模板** - 后端API + 服务端渲染前端页面
*   **前端:** **HTML + Bootstrap + JavaScript** - 简单直接，无需复杂构建工具
*   **Excel解析:** **Pandas** - 强大的数据处理库
*   **Jira-API:** **`jira` (Python Library)** - 官方推荐的Python库
*   **数据库:** **SQLAlchemy + PyMySQL** - MySQL数据库操作
*   **部署:** **单个Docker容器** - 包含所有功能，一键部署

### 2.2. 备选方案：Node.js全栈

如果您更熟悉Node.js，也可以选择：

*   **Web框架:** **Express.js + EJS模板**
*   **前端:** **HTML + Bootstrap + JavaScript**
*   **Excel解析:** **xlsx** 或 **node-xlsx**
*   **Jira-API:** **jira-client** 或直接使用 **axios** 调用REST API
*   **数据库:** **mysql2** 或 **sequelize**

### 2.3. 部署优势

*   **单容器部署** - 只需一个Docker容器
*   **无需Nginx** - 直接使用应用服务器
*   **快速启动** - 一条命令即可运行
*   **易于维护** - 技术栈统一，减少复杂性

## 3. 核心功能模块

### 3.1. 前端界面（服务端渲染）

使用FastAPI + Jinja2模板，创建简单高效的Web界面：

#### 3.1.1. 主页面 (`/`)

*   **文件上传区:** 
    *   HTML5文件上传组件，支持拖拽上传
    *   使用Bootstrap样式，美观易用
    *   JavaScript处理文件上传和进度显示

*   **配置区域:**
    *   Jira配置表单（项目KEY、用户名、Token等）
    *   可折叠的高级配置选项
    *   实时配置验证

#### 3.1.2. 解析结果页面 (`/preview`)

*   **任务预览表格:**
    *   Bootstrap Table组件展示解析结果
    *   支持行编辑和数据校验
    *   层次结构展示主任务和子任务关系
    *   人员信息格式化显示

*   **操作按钮:**
    *   确认并创建任务
    *   返回重新上传
    *   导出预览结果

#### 3.1.3. 创建结果页面 (`/result`)

*   **进度显示:**
    *   实时进度条和状态更新
    *   WebSocket或Server-Sent Events实现实时更新

*   **结果汇总:**
    *   成功/失败统计
    *   详细的任务创建结果表格
    *   错误信息和建议

#### 3.1.4. 页面特性

*   **响应式设计:** 支持桌面和移动设备
*   **无刷新操作:** 使用AJAX实现流畅交互
*   **简洁美观:** Bootstrap + 自定义CSS
*   **易于维护:** 模板化HTML，逻辑清晰

### 3.2. 后端 API

后端需要提供两个核心 API 端点，分别用于解析文件和创建任务。

#### 3.2.1. 文件解析接口

`POST /api/v1/parse-excel`

*   **请求 (Request):**
    *   `Content-Type`: `multipart/form-data`
    *   `Body`: 包含上传的 Excel 文件

*   **响应 (Response):**
    *   **成功 (HTTP 200):**
        ```json
        {
          "status": "success",
          "total": 5,
          "tasks": [
            {
              "row": 2,
              "orange_card": "JGKEZH-23876",
              "module": "《MCP追加第四、五大点》",
              "main_task": "MCP优化",
              "sub_task": "web端 应用文案全部替换为fundx应用",
              "task_type": "UI",
              "task_type_id": "11012",
              "assignee": "guanyuan",
              "assignee_display": "关远",
              "workload": "0.3",
              "sprint": "INST2025-sprint10"
            },
            {
              "row": 3,
              "orange_card": "",
              "module": "",
              "main_task": "",
              "sub_task": "cms端 应用文案全部替换为fundx应用",
              "task_type": "UI",
              "task_type_id": "11012",
              "assignee": "guanyuan",
              "assignee_display": "关远",
              "workload": "0.1",
              "sprint": "INST2025-sprint10"
            }
          ]
        }
        ```
    *   **失败 (HTTP 400/500):**
        ```json
        {
          "status": "error",
          "message": "文件格式不正确或无法解析。"
        }
        ```

#### 3.2.2. 任务创建接口

`POST /api/v1/create-issues`

*   **请求 (Request):**
    *   `Content-Type`: `application/json`
    *   `Body`: 
        ```json
        {
          "jira_config": {
            "server_url": "https://your-domain.atlassian.net",
            "username": "<EMAIL>",
            "api_token": "your_jira_api_token_here",
            "project_key": "PROJ",
            "environment": "UAT"
          },
          "tasks": [
            {
              "row": 2,
              "orange_card": "JGKEZH-23876",
              "module": "《MCP追加第四、五大点》",
              "main_task": "MCP优化",
              "sub_task": "web端 应用文案全部替换为fundx应用",
              "task_type": "UI",
              "task_type_id": "11012",
              "assignee": "guanyuan",
              "assignee_display": "关远",
              "workload": "0.3",
              "sprint": "INST2025-sprint10"
            }
          ]
        }
        ```

*   **响应 (Response):**
    *   **成功 (HTTP 200):**
        ```json
        {
          "status": "success",
          "total": 5,
          "success_count": 4,
          "failed_count": 2,
          "main_tasks_created": 1,
          "sub_tasks_created": 3,
          "sprint_linked": 1,
          "results": [
            { 
              "row": 2, 
              "status": "success", 
              "type": "main_task",
              "issue_key": "PROJ-123", 
              "summary": "MCP优化", 
              "link": "https://your-jira.com/browse/PROJ-123",
              "demand_linked": true,
              "demand_key": "JGKEZH-23876"
            },
            { 
              "row": 2, 
              "status": "success", 
              "type": "sub_task",
              "issue_key": "PROJ-124", 
              "summary": "web端 应用文案全部替换为fundx应用", 
              "parent_key": "PROJ-123",
              "link": "https://your-jira.com/browse/PROJ-124" 
            },
            { 
              "row": 3, 
              "status": "failed", 
              "type": "sub_task",
              "summary": "cms端 应用文案全部替换为fundx应用", 
              "error": "Assignee 'invalid_user' does not exist." 
            },
            { 
              "row": 4, 
              "status": "failed", 
              "type": "main_task",
              "summary": "MCP优化", 
              "error": "需求JGKEZH-23876关联失败: Issue does not exist or you do not have permission to link it." 
            },
            { 
              "row": 5, 
              "status": "failed", 
              "type": "sprint_link",
              "summary": "关联迭代", 
              "error": "迭代关联失败,未查找到迭代INST2025-sprint10" 
            }
          ]
        }
        ```
    *   **失败 (HTTP 400/500):**
        ```json
        {
          "status": "error",
          "message": "Jira连接失败或任务创建失败。"
        }
        ```

### 3.3. Excel 文件解析

这是业务逻辑的核心部分。

1.  **定义模板:** Excel 文件的列名必须是固定的：`橙卡`、`模块`、`主任务`、`子任务`、`任务类型`、`负责人`、`工作量`、`迭代`。
2.  **读取文件:** 后端接收到文件后，使用 `pandas.read_excel()` 将其读入一个 DataFrame。
3.  **数据清洗和验证:**
    *   检查必需的列（如 `子任务`, `任务类型`, `负责人`, `迭代`）是否存在。
    *   对 `负责人` 等字段进行预处理（如去除首尾空格）。
    *   通过数据库查询将中文姓名转换为对应的OA账号。
    *   处理空单元格：空的`主任务`单元格会自动填充为上一行的主任务值。
    *   支持主子任务层次结构：根据`主任务`列自动分组和关联子任务。
    *   任务类型自动解析：将表格中的任务类型（UI、API、测试）映射为对应的Jira子任务类型ID。
4.  **人员信息处理:**
    *   从MySQL数据库中查询人员的中文姓名和OA账号映射关系。
    *   返回`assignee`字段（OA账号/英文用户名）和`assignee_display`字段（中文姓名）。
    *   前端负责拼接展示为"张三(zhangsan)"的格式。
    *   验证负责人是否存在于数据库中。

### 3.4. Jira 任务创建

1.  **安全配置:** Jira 的服务器地址、用户名和 API Token **绝不能**硬编码在代码中。它们应该通过环境变量（如 `.env` 文件）来管理。
2.  **连接 Jira:** 使用 `jira` 库初始化 Jira 客户端。
3.  **遍历 Dataframe:** 循环遍历从 Excel 解析出的每一行数据。
4.  **构建 Issue 字典:** 为每一行数据，创建一个符合 `jira` 库要求的 `dict`。
    ```python
    issue_dict = {
        'project': {'key': 'PROJ'},
        'summary': '这是一个任务摘要',
        'issuetype': {'name': 'Task'},
        'assignee': {'name': 'johndoe'},
        # 更多自定义字段...
    }
    ```
    主任务构建dict
    ```python
    #主任务（迭代功能）
    issue_dict = {
      'fields': {
          'priority': {
              'id': '3'
          },
          # 主任务摘要
          'summary': item['main_task'],
          'issuetype': {
              'id': '11007'
          },
          'project': {
              'key': project
          },
          'reporter': {
              'name': usr
          },
          # 经办人
          'assignee': {
              'name': item['assignee']
          },
        # 开发责任人
        'customfield_13103':{
            'name': item['assignee']
        },
        # 测试责任人
        'customfield_11305':{
            'name': 'zhouqishu'
        }
      }}
      ```
             子任务创建dict
       ```python
       # 任务类型映射
       task_type_mapping = {
           'UI': '11012',      # 前端开发子任务
           'API': '11013',     # 后端开发子任务
           '数据': '11014',     # 数据开发子任务
           '测试': '11015'      # 测试开发子任务
       }
       
       # 创建子任务
       payload = {
       'fields': {
           'priority': {
               'id': '3'
           },
           # 子任务摘要
           'summary': item['sub_task'],
           # 任务类型 - 根据表格内容自动解析
           'issuetype': {
               'id': task_type_mapping.get(item['task_type'], '11012')  # 默认为前端开发子任务
           },
           # 关联主任务
           'parent': {
             'key': taskMapJson[item['main_task']]['key']
           },
           'project': {
               'key': project
           },
           # 报告人
           'reporter': {
               'name': usr
           },
           'assignee': {
               'name': item['assignee']
           }        ,
           # 预估耗时
         'timetracking': {
             'originalEstimate': item['time'],
             'remainingEstimate': item['time']
         }}}
       ```
5.  **调用 API:** 调用 `jira.create_issue(fields=issue_dict)` 来创建任务。
6.  **需求关联:** 创建主任务后，使用Issue Links API将主任务与需求JIRA建立关联：
    ```python
    # 更新主任务，关联需求JIRA
    payload = {
        'update': {
            'issuelinks': [
                {
                    'add': {
                        'type': {
                            'name': '需求关联',
                            'inward': '需求关联',
                            'outward': '需求关联'
                        },
                        'inwardIssue': {
                            'key': demand  # 橙卡中的需求JIRA Key
                        }
                    }
                }
            ]
        }
    }
    response = session.put(issueUrl, json=payload, headers=headers)
    if response.status_code != 204:
        failDataJson.append({"任务": key, "原因": "需求" + demand + "关联失败:" + response.text})
    ```
7.  **Sprint关联:** 创建任务后，根据迭代名称查询Sprint ID并关联任务：
    ```python
    # 查询Sprint ID
    sprintUrl = url + '/rest/greenhopper/1.0/sprint/picker?query=' + sprint
    resp = session.get(sprintUrl, headers=headers)
    if resp.status_code != 200:
        failDataJson.append({"任务": "关联迭代", "原因": "迭代关联失败,未查找到迭代"})
    else:
        resp_data = resp.json()
        sprintId = 0
        if len(resp_data['suggestions']) >= 1:
            sprintId = resp_data['suggestions'][0]['id']
        elif len(resp_data['allMatches']) >= 1:
            sprintId = resp_data['allMatches'][0]['id']
        else:
            failDataJson.append({"任务": "关联迭代", "原因": "迭代关联失败,未查找到迭代"})
            sprintId = -1
        
        # 关联任务到Sprint
        if sprintId > 0:
            for key, item in taskMapJson.items():
                issueUrl = url + '/rest/api/latest/issue/' + item["key"]
                payload = {
                    'fields': {
                        'customfield_10005': sprintId  # Sprint字段
                    }
                }
                response = session.put(issueUrl, json=payload, headers=headers)
                if response.status_code != 204:
                    failDataJson.append({"任务": item['main_task'], "原因": "迭代关联失败:" + response.text})
    ```
8.  **错误处理:** 使用 `try...except` 块来捕获创建过程中可能发生的任何异常（如网络问题、无效的用户、权限不足、需求关联失败、Sprint关联失败等），并记录详细的错误信息。

## 4. 项目结构

### 4.1. 纯Python全栈项目结构（推荐）

```
.
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI 应用主入口
│   ├── routers/             # 路由模块
│   │   ├── __init__.py
│   │   ├── web.py           # Web页面路由
│   │   └── api.py           # API路由
│   ├── services/            # 业务逻辑服务
│   │   ├── __init__.py
│   │   ├── excel_parser.py  # Excel 解析逻辑
│   │   ├── jira_service.py  # Jira API 交互逻辑
│   │   ├── user_service.py  # 用户信息查询服务
│   │   └── task_type_service.py # 任务类型映射服务
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   ├── schemas.py       # Pydantic 数据模型
│   │   └── database.py      # SQLAlchemy 数据库模型
│   ├── database/            # 数据库相关
│   │   ├── __init__.py
│   │   └── connection.py    # 数据库连接管理
│   ├── templates/           # Jinja2 HTML模板
│   │   ├── base.html        # 基础模板
│   │   ├── index.html       # 主页
│   │   ├── preview.html     # 预览页面
│   │   └── result.html      # 结果页面
│   ├── static/              # 静态文件
│   │   ├── css/
│   │   │   └── style.css    # 自定义样式
│   │   ├── js/
│   │   │   └── app.js       # JavaScript逻辑
│   │   └── uploads/         # 临时上传文件目录
│   └── core/
│       ├── __init__.py
│       └── config.py        # 配置管理
├── requirements.txt         # Python 依赖
├── .env.example            # 环境变量模板
├── Dockerfile              # Docker 构建文件
└── README.md               # 项目说明
```

### 4.2. Node.js全栈项目结构（备选）

```
.
├── src/
│   ├── app.js              # Express 应用主入口
│   ├── routes/             # 路由模块
│   │   ├── web.js          # Web页面路由
│   │   └── api.js          # API路由
│   ├── services/           # 业务逻辑服务
│   │   ├── excelParser.js  # Excel 解析逻辑
│   │   ├── jiraService.js  # Jira API 交互逻辑
│   │   ├── userService.js  # 用户信息查询服务
│   │   └── taskTypeService.js # 任务类型映射服务
│   ├── models/             # 数据模型
│   │   └── database.js     # 数据库模型
│   ├── database/           # 数据库相关
│   │   └── connection.js   # 数据库连接管理
│   ├── views/              # EJS 模板
│   │   ├── layout.ejs      # 基础模板
│   │   ├── index.ejs       # 主页
│   │   ├── preview.ejs     # 预览页面
│   │   └── result.ejs      # 结果页面
│   ├── public/             # 静态文件
│   │   ├── css/
│   │   ├── js/
│   │   └── uploads/
│   └── config/
│       └── config.js       # 配置管理
├── package.json            # Node.js 依赖
├── .env.example           # 环境变量模板
├── Dockerfile             # Docker 构建文件
└── README.md              # 项目说明
```

## 5. 快速实现步骤

### 5.1. Python全栈快速搭建（推荐）

1.  **步骤 1: 环境搭建（5分钟）**
    ```bash
    # 创建项目目录
    mkdir jira-task-creator && cd jira-task-creator
    
    # 创建虚拟环境
    python -m venv venv
    source venv/bin/activate  # Linux/Mac
    # venv\Scripts\activate   # Windows
    
    # 安装依赖
    pip install fastapi uvicorn jinja2 python-multipart pandas python-jira sqlalchemy pymysql python-dotenv
    ```

2.  **步骤 2: 核心文件创建（10分钟）**
    *   创建 `app/main.py` - FastAPI主应用
    *   创建 `app/routers/web.py` - Web页面路由
    *   创建 `app/routers/api.py` - API路由
    *   创建基础HTML模板和静态文件

3.  **步骤 3: 业务逻辑实现（30分钟）**
    *   实现Excel解析服务
    *   实现用户信息查询（可先用硬编码测试）
    *   实现Jira服务基础功能
    *   实现任务类型映射

4.  **步骤 4: 前端页面开发（20分钟）**
    *   创建文件上传页面
    *   创建任务预览页面
    *   创建结果展示页面
    *   添加Bootstrap样式和基础JavaScript

5.  **步骤 5: 集成测试（10分钟）**
    *   使用测试Excel文件验证解析功能
    *   连接测试Jira环境验证任务创建
    *   优化错误处理和用户体验

6.  **步骤 6: Docker部署（5分钟）**
    ```dockerfile
    FROM python:3.9-slim
    WORKDIR /app
    COPY requirements.txt .
    RUN pip install -r requirements.txt
    COPY . .
    EXPOSE 8000
    CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
    ```

### 5.2. Node.js全栈快速搭建（备选）

1.  **步骤 1: 环境搭建（5分钟）**
    ```bash
    # 创建项目
    mkdir jira-task-creator && cd jira-task-creator
    npm init -y
    
    # 安装依赖
    npm install express ejs multer xlsx mysql2 axios dotenv
    npm install -D nodemon
    ```

2.  **步骤 2-6: 类似Python方案**
    *   使用Express框架和EJS模板
    *   使用xlsx库解析Excel
    *   使用axios调用Jira API
    *   使用mysql2连接数据库

### 5.3. 部署命令

```bash
# 构建并运行Docker容器
docker build -t jira-task-creator .
docker run -p 8000:8000 --env-file .env jira-task-creator

# 或直接运行
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 5.4. 预计时间

*   **总开发时间:** 1-2小时
*   **部署时间:** 5-10分钟
*   **测试时间:** 30分钟

这样您可以在半天内就有一个可用的原型进行试用！

## 6. 配置文件示例 (`.env.example`)

将此文件复制为 `.env` 并填入真实信息。

```bash
# Jira Server Configuration
JIRA_SERVER_URL="https://your-domain.atlassian.net"
JIRA_USERNAME="<EMAIL>"
JIRA_API_TOKEN="your_jira_api_token_here"

# Database Configuration
DATABASE_URL="mysql+pymysql://username:password@localhost:3306/database_name"
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="username"
DB_PASSWORD="password"
DB_NAME="database_name"
```

## 7. 数据库表结构示例

### 7.1. 用户信息表 (`users` 或 `employees`)

建议的用户信息表结构：

```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    oa_account VARCHAR(50) NOT NULL UNIQUE COMMENT 'OA账号/英文用户名',
    chinese_name VARCHAR(100) NOT NULL COMMENT '中文姓名',
    email VARCHAR(100) COMMENT '邮箱地址',
    department VARCHAR(100) COMMENT '部门',
    status TINYINT DEFAULT 1 COMMENT '状态：1-在职，0-离职',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO users (oa_account, chinese_name, email, department) VALUES
('guanyuan', '关远', '<EMAIL>', 'UI设计部'),
('linwenjie', '林文杰', '<EMAIL>', '后端开发部'),
('chenhe', '陈贺', '<EMAIL>', '测试部');
```

### 7.2. 用户信息查询服务示例

```python
# backend/app/services/user_service.py
from sqlalchemy import text
from backend.app.database.connection import get_db_connection

class UserService:
    @staticmethod
    def get_user_by_name(chinese_name: str):
        """根据中文姓名查询用户信息"""
        with get_db_connection() as conn:
            query = text("SELECT oa_account, chinese_name FROM users WHERE chinese_name = :name AND status = 1")
            result = conn.execute(query, {"name": chinese_name}).fetchone()
            if result:
                return {
                    "assignee": result.oa_account,
                    "assignee_display": result.chinese_name
                }
            return None
    
    @staticmethod
    def get_user_by_oa_account(oa_account: str):
        """根据OA账号查询用户信息"""
        with get_db_connection() as conn:
            query = text("SELECT oa_account, chinese_name FROM users WHERE oa_account = :account AND status = 1")
            result = conn.execute(query, {"account": oa_account}).fetchone()
            if result:
                return {
                    "assignee": result.oa_account,
                    "assignee_display": result.chinese_name
                }
            return None
    
    @staticmethod
    def validate_users(user_list: list):
        """批量验证用户是否存在"""
        if not user_list:
            return {}
        
        with get_db_connection() as conn:
            placeholders = ','.join([':user' + str(i) for i in range(len(user_list))])
            query = text(f"SELECT oa_account, chinese_name FROM users WHERE chinese_name IN ({placeholders}) AND status = 1")
            params = {f'user{i}': user for i, user in enumerate(user_list)}
            results = conn.execute(query, params).fetchall()
            
            return {result.chinese_name: {
                "assignee": result.oa_account,
                "assignee_display": result.chinese_name
                         } for result in results}
```

### 7.3. 任务类型映射服务示例

```python
# backend/app/services/task_type_service.py
class TaskTypeService:
    # 任务类型映射表
    TASK_TYPE_MAPPING = {
        'UI': '11012',      # 前端开发子任务
        'API': '11013',     # 后端开发子任务
        '测试': '11014'      # 数据开发子任务
    }
    
    # 主任务类型ID
    MAIN_TASK_TYPE_ID = '11007'  # 迭代任务类型
    
    @staticmethod
    def get_main_task_type_id():
        """获取主任务类型ID"""
        return TaskTypeService.MAIN_TASK_TYPE_ID
    
    @staticmethod
    def get_sub_task_type_id(task_type: str):
        """根据任务类型获取子任务类型ID"""
        return TaskTypeService.TASK_TYPE_MAPPING.get(task_type, '11012')  # 默认为前端开发子任务
    
    @staticmethod
    def get_all_task_types():
        """获取所有支持的任务类型"""
        return list(TaskTypeService.TASK_TYPE_MAPPING.keys())
    
    @staticmethod
    def validate_task_type(task_type: str):
        """验证任务类型是否有效"""
        return task_type in TaskTypeService.TASK_TYPE_MAPPING
    
    @staticmethod
    def get_task_type_display_name(task_type: str):
        """获取任务类型的显示名称"""
        display_mapping = {
            'UI': '前端开发',
            'API': '后端开发',
            '测试': '数据开发'
        }
        return display_mapping.get(task_type, task_type)
```

## 8. Excel 模板示例 (`template.xlsx`)

根据实际业务需求，模板应包含以下列：

| 橙卡 | 模块 | 主任务 | 子任务 | 任务类型 | 负责人 | 工作量 | 迭代 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| JGKEZH-23876 | 《MCP追加第四、五大点》 | MCP优化 | web端 应用文案全部替换为fundx应用 | UI | 关远 | 0.3 | INST2025-sprint10 |
| | | | cms端 应用文案全部替换为fundx应用 | UI | 关远 | 0.1 | INST2025-sprint10 |
| | | | MCP优化 | 测试 | 陈贺 | 1 | INST2025-sprint10 |
| | | | CMS自动生成【MCP介绍】 | UI | 关远 | 1 | INST2025-sprint10 |
| | | | mcp服务可用工具列表接口 | API | 林文杰 | 1 | INST2025-sprint10 |

**列说明:**
*   `橙卡`: 需求的JIRA Key（如JGKEZH-23876），用于关联需求任务
*   `模块`: 功能模块名称，用于分类
*   `主任务`: 主要功能任务名称，系统会自动创建为主任务（迭代功能类型）
*   `子任务`: 具体的子任务名称，会作为主任务的子任务创建
*   `任务类型`: 任务类型，如UI、API、测试等，系统会自动解析为对应的Jira子任务类型ID
*   `负责人`: 任务负责人的中文姓名（系统会自动查询对应的OA账号）
*   `工作量`: 预估工作量（天），用于时间跟踪
*   `迭代`: Sprint名称，用于任务分配到对应迭代（通过Greenhopper API查询Sprint ID并关联任务）

**处理逻辑:**
1. 系统会根据`主任务`列的值自动创建主任务（如果不存在），任务类型为迭代任务类型（ID: 11007）
2. 每个`子任务`都会作为对应`主任务`的子任务创建，根据`任务类型`字段自动解析：
   - UI → 前端开发子任务（ID: 11012）
   - API → 后端开发子任务（ID: 11013）
   - 测试 → 数据开发子任务（ID: 11014）
3. 相同`主任务`名称的行会归属到同一个主任务下
4. 空的`主任务`单元格会沿用上一行的主任务名称
5. 创建主任务后，会自动与`橙卡`字段中的需求JIRA建立"需求关联"链接
6. 根据`迭代`字段的Sprint名称，查询对应的Sprint ID并将任务关联到该Sprint

---

此方案提供了一个从概念到实施的完整蓝图。您可以根据团队的熟悉度和具体需求微调技术选型和功能细节。 