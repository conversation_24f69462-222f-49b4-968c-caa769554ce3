#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: test_jira_direct.py
描述: 直接模拟JIRA API请求测试

使用方法:
python test_jira_direct.py
"""

import requests
import json

def test_jira_create_issue():
    """直接测试JIRA创建任务API"""
    
    # JIRA配置
    jira_url = "http://jirauat.gf.com.cn"
    token = "OTA3ODQ3MzI3MjgwOvqvwJR5zufAp/ibk9NkjWqAaTw+"
    
    # API端点
    create_url = f"{jira_url}/rest/api/latest/issue"
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    # 请求体 - 基于之前的测试数据
    issue_data = {
        "fields": {
            "project": {
                "key": "JGKEZH"
            },
            "summary": "测试主任务创建",
            "issuetype": {
                "id": "11007"  # 迭代功能
            },
            "priority": {
                "id": "3"  # 中等优先级
            },
            "reporter": {
                "name": "lidezheng"
            },
            "assignee": {
                "name": "guanyuan"
            },
            "customfield_13103": {  # 开发责任人
                "name": "guanyuan"
            },
            "customfield_11305": {  # 测试责任人
                "name": "zhouqishu"
            }
        }
    }
    
    print("=" * 80)
    print("直接测试JIRA API创建任务")
    print("=" * 80)
    print(f"请求URL: {create_url}")
    print(f"请求方法: POST")
    print(f"请求头: {headers}")
    print("请求体:")
    print(json.dumps(issue_data, indent=2, ensure_ascii=False))
    print("=" * 80)
    
    try:
        # 发送请求
        response = requests.post(
            create_url,
            json=issue_data,
            headers=headers,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("响应体:")
        
        if response.text:
            try:
                response_json = response.json()
                print(json.dumps(response_json, indent=2, ensure_ascii=False))
                
                if response.status_code == 201:
                    print(f"\n✅ 任务创建成功!")
                    print(f"JIRA Key: {response_json.get('key', 'N/A')}")
                    print(f"任务链接: {jira_url}/browse/{response_json.get('key', '')}")
                else:
                    print(f"\n❌ 任务创建失败!")
                    if 'errors' in response_json:
                        print("错误详情:")
                        for field, error in response_json['errors'].items():
                            print(f"  {field}: {error}")
                    if 'errorMessages' in response_json:
                        print("错误消息:")
                        for msg in response_json['errorMessages']:
                            print(f"  - {msg}")
                            
            except json.JSONDecodeError:
                print(response.text)
        else:
            print("(空响应)")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def test_jira_connection():
    """测试JIRA连接"""
    
    jira_url = "http://jirauat.gf.com.cn"
    token = "OTA3ODQ3MzI3MjgwOvqvwJR5zufAp/ibk9NkjWqAaTw+"
    
    # 测试端点
    test_url = f"{jira_url}/rest/api/2/myself"
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/json'
    }
    
    print("=" * 80)
    print("测试JIRA连接")
    print("=" * 80)
    print(f"测试URL: {test_url}")
    print(f"请求头: {headers}")
    print("=" * 80)
    
    try:
        response = requests.get(test_url, headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("响应体:")
        
        if response.text:
            try:
                response_json = response.json()
                print(json.dumps(response_json, indent=2, ensure_ascii=False))
                
                if response.status_code == 200:
                    print(f"\n✅ JIRA连接成功!")
                    print(f"用户: {response_json.get('displayName', 'N/A')}")
                    print(f"邮箱: {response_json.get('emailAddress', 'N/A')}")
                else:
                    print(f"\n❌ JIRA连接失败!")
                    
            except json.JSONDecodeError:
                print(response.text)
        else:
            print("(空响应)")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def test_jira_project_info():
    """测试获取项目信息"""
    
    jira_url = "http://jirauat.gf.com.cn"
    token = "OTA3ODQ3MzI3MjgwOvqvwJR5zufAp/ibk9NkjWqAaTw+"
    
    # 项目信息端点
    project_url = f"{jira_url}/rest/api/2/project/JGKEZH"
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/json'
    }
    
    print("=" * 80)
    print("测试获取项目信息")
    print("=" * 80)
    print(f"项目URL: {project_url}")
    print(f"请求头: {headers}")
    print("=" * 80)
    
    try:
        response = requests.get(project_url, headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print("响应体:")
        
        if response.text:
            try:
                response_json = response.json()
                print(json.dumps(response_json, indent=2, ensure_ascii=False))
                
                if response.status_code == 200:
                    print(f"\n✅ 项目信息获取成功!")
                    print(f"项目名称: {response_json.get('name', 'N/A')}")
                    print(f"项目Key: {response_json.get('key', 'N/A')}")
                else:
                    print(f"\n❌ 项目信息获取失败!")
                    
            except json.JSONDecodeError:
                print(response.text)
        else:
            print("(空响应)")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def main():
    """主函数"""
    print("JIRA API直接测试工具")
    print("=" * 80)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试JIRA连接")
        print("2. 测试获取项目信息")
        print("3. 测试创建任务")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            test_jira_connection()
        elif choice == '2':
            test_jira_project_info()
        elif choice == '3':
            test_jira_create_issue()
        elif choice == '4':
            print("退出测试工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
