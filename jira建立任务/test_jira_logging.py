#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: test_jira_logging.py
描述: 测试JIRA接口调用日志记录功能

使用方法:
python test_jira_logging.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.logging import setup_logging
from app.services.jira_service import JiraService

def test_jira_connection():
    """测试JIRA连接"""
    print("=" * 60)
    print("测试JIRA连接日志记录")
    print("=" * 60)
    
    # 初始化日志系统
    setup_logging()
    
    # 创建JIRA服务实例
    jira_service = JiraService()
    
    # 测试配置（请根据实际情况修改）
    test_config = {
        'environment': 'test',
        'username': 'test_user',
        'token': 'test_token',
        'project_key': 'TEST'
    }
    
    print("正在测试JIRA连接...")
    print("请查看logs/jira_api_*.log文件查看详细的API调用日志")
    
    # 执行连接测试
    result = jira_service.test_jira_connection(test_config)
    
    print(f"连接测试结果: {result}")
    print("\n请检查以下日志文件:")
    print("- logs/app_*.log (应用日志)")
    print("- logs/jira_api_*.log (JIRA API专用日志)")

def test_jira_task_creation():
    """测试JIRA任务创建"""
    print("=" * 60)
    print("测试JIRA任务创建日志记录")
    print("=" * 60)
    
    # 初始化日志系统
    setup_logging()
    
    # 创建JIRA服务实例
    jira_service = JiraService()
    
    # 测试配置
    test_config = {
        'environment': 'test',
        'username': 'test_user',
        'token': 'test_token',
        'project_key': 'TEST',
        'sprint': 'Sprint 1'
    }
    
    # 测试任务数据
    test_tasks = [
        {
            'main_task': '测试主任务1',
            'sub_task': '测试子任务1',
            'assignee': '张三',
            'demand': 'TEST-001',
            'module': '测试模块',
            'workload': '2',
            'task_type': '开发任务'
        },
        {
            'main_task': '测试主任务2',
            'sub_task': '测试子任务2',
            'assignee': '李四',
            'demand': 'TEST-002',
            'module': '测试模块2',
            'workload': '3',
            'task_type': '测试任务'
        }
    ]
    
    print("正在测试JIRA任务创建...")
    print("请查看logs/jira_api_*.log文件查看详细的API调用日志")
    
    # 执行批量创建
    result = jira_service.batch_create_issues(test_config, test_tasks)
    
    print(f"任务创建结果: {result}")
    print("\n请检查以下日志文件:")
    print("- logs/app_*.log (应用日志)")
    print("- logs/jira_api_*.log (JIRA API专用日志)")

def main():
    """主函数"""
    print("JIRA接口调用日志记录测试工具")
    print("=" * 60)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试JIRA连接")
        print("2. 测试JIRA任务创建")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            test_jira_connection()
        elif choice == '2':
            test_jira_task_creation()
        elif choice == '3':
            print("退出测试工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
