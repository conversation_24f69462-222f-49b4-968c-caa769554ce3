<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>姓名映射功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>姓名映射功能测试</h2>
        
        <!-- 搜索测试 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="testSearch" class="form-label">搜索测试</label>
                <input type="text" class="form-control" id="testSearch" placeholder="输入姓名或OA账号...">
            </div>
            <div class="col-md-6">
                <label class="form-label">测试按钮</label><br>
                <button type="button" class="btn btn-primary" id="testAddBtn">测试添加</button>
                <button type="button" class="btn btn-secondary" id="testLoadBtn">测试加载</button>
            </div>
        </div>
        
        <!-- 结果显示 -->
        <div class="row">
            <div class="col-12">
                <div id="testResults" class="border p-3">
                    <h5>测试结果：</h5>
                    <div id="testOutput">等待测试...</div>
                </div>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="row mt-4">
            <div class="col-12">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>姓名</th>
                            <th>OA账号</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="testTableBody">
                        <!-- 数据将在这里显示 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        var testData = [];
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            
            // 初始化事件监听器
            initTestEvents();
            
            // 自动加载数据
            loadTestData();
        });
        
        // 初始化事件监听器
        function initTestEvents() {
            // 搜索功能测试
            var searchInput = document.getElementById('testSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    var searchTerm = this.value.toLowerCase().trim();
                    console.log('搜索输入:', searchTerm);
                    filterTestData(searchTerm);
                    updateTestOutput('搜索: ' + searchTerm);
                });
            }
            
            // 测试加载按钮
            var loadBtn = document.getElementById('testLoadBtn');
            if (loadBtn) {
                loadBtn.addEventListener('click', function() {
                    console.log('点击测试加载按钮');
                    loadTestData();
                });
            }
            
            // 测试添加按钮
            var addBtn = document.getElementById('testAddBtn');
            if (addBtn) {
                addBtn.addEventListener('click', function() {
                    console.log('点击测试添加按钮');
                    testAddMapping();
                });
            }
        }
        
        // 加载测试数据
        function loadTestData() {
            updateTestOutput('正在加载数据...');
            
            fetch('/api/v1/jira/name-mapping')
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    console.log('加载数据成功:', data);
                    if (data.success && data.data) {
                        testData = data.data;
                        displayTestData(testData);
                        updateTestOutput('成功加载 ' + data.count + ' 条记录');
                    } else {
                        updateTestOutput('加载数据失败');
                    }
                })
                .catch(function(error) {
                    console.error('加载数据失败:', error);
                    updateTestOutput('加载数据失败: ' + error.message);
                });
        }
        
        // 显示测试数据
        function displayTestData(data) {
            var tbody = document.getElementById('testTableBody');
            tbody.innerHTML = '';
            
            data.forEach(function(mapping) {
                var row = document.createElement('tr');
                row.innerHTML = 
                    '<td>' + mapping.id + '</td>' +
                    '<td><strong>' + mapping.name + '</strong></td>' +
                    '<td><code>' + mapping.login + '</code></td>' +
                    '<td><button class="btn btn-sm btn-outline-danger" onclick="testDeleteMapping(' + mapping.id + ')">删除</button></td>';
                tbody.appendChild(row);
            });
        }
        
        // 过滤测试数据
        function filterTestData(searchTerm) {
            var filteredData;
            if (!searchTerm) {
                filteredData = testData.slice();
            } else {
                filteredData = testData.filter(function(mapping) {
                    return mapping.name.toLowerCase().indexOf(searchTerm) !== -1 || 
                           mapping.login.toLowerCase().indexOf(searchTerm) !== -1;
                });
            }
            displayTestData(filteredData);
        }
        
        // 测试添加映射
        function testAddMapping() {
            var testName = '测试' + Date.now();
            var testLogin = 'test' + Date.now();
            
            updateTestOutput('正在添加测试数据: ' + testName + ' -> ' + testLogin);
            
            fetch('/api/v1/jira/name-mapping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: testName,
                    login: testLogin
                })
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                console.log('添加数据成功:', data);
                if (data.success) {
                    updateTestOutput('添加成功: ' + data.message);
                    loadTestData(); // 重新加载数据
                } else {
                    updateTestOutput('添加失败: ' + data.message);
                }
            })
            .catch(function(error) {
                console.error('添加数据失败:', error);
                updateTestOutput('添加失败: ' + error.message);
            });
        }
        
        // 测试删除映射
        function testDeleteMapping(id) {
            if (!confirm('确定要删除这个测试映射吗？')) {
                return;
            }
            
            updateTestOutput('正在删除映射 ID: ' + id);
            
            fetch('/api/v1/jira/name-mapping/' + id, {
                method: 'DELETE'
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                console.log('删除数据成功:', data);
                if (data.success) {
                    updateTestOutput('删除成功: ' + data.message);
                    loadTestData(); // 重新加载数据
                } else {
                    updateTestOutput('删除失败: ' + data.message);
                }
            })
            .catch(function(error) {
                console.error('删除数据失败:', error);
                updateTestOutput('删除失败: ' + error.message);
            });
        }
        
        // 更新测试输出
        function updateTestOutput(message) {
            var output = document.getElementById('testOutput');
            var timestamp = new Date().toLocaleTimeString();
            output.innerHTML = '[' + timestamp + '] ' + message;
        }
    </script>
</body>
</html>
