# 环境配置示例文件
# 复制此文件为 .env 并修改相应配置

# 应用环境 (development/production)
ENVIRONMENT=development

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=true

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=jira_tasks
DB_USERNAME=root
DB_PASSWORD=your_password

# Jira配置（可选，也可以在界面中配置）
JIRA_SERVER=https://your-jira-server.com
JIRA_USERNAME=your_username
JIRA_PASSWORD=your_password
JIRA_PROJECT_KEY=PROJECT

# 文件上传配置
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_EXTENSIONS=.xlsx,.xls

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 安全配置
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000,http://localhost:8080 