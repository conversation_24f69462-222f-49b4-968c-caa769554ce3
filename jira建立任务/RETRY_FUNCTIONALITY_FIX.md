# 重试功能修复说明

## 问题描述

点击"重试所有任务"后，任务信息没有完整传递到预览页面，只显示任务类型和负责人，缺少需求、模块、工作量等重要信息。

## 问题原因

1. **失败任务数据结构不完整**: 原来的失败任务只包含基本信息（title、assignee、task_type、error），缺少完整的原始任务数据。

2. **重试数据转换不正确**: 前端重试功能直接使用失败任务数据，没有转换为预览页面需要的格式。

## 修复方案

### 1. 后端修复 - 完善失败任务数据结构

**修改文件**: `app/services/jira_service.py`

#### 主任务失败数据结构
```python
failed_result = {
    "success": False,
    "type": "main_task",
    "title": main_task_name,
    "assignee": main_task_info.get('assignee', ''),
    "task_type": "主任务",
    "error": error_msg,
    # 添加完整的原始任务信息，用于重试
    "demand": main_task_info.get('demand', ''),
    "module": main_task_info.get('module', ''),
    "main_task": main_task_name,
    "sub_task": "",  # 主任务没有子任务名称
    "workload": main_task_info.get('workload', '')
}
```

#### 子任务失败数据结构
```python
failed_result = {
    "success": False,
    "type": "sub_task",
    "title": sub_task_name,
    "main_task": main_task_name,
    "assignee": sub_task_info.get('assignee', ''),
    "task_type": sub_task_info.get('task_type', ''),
    "error": error_msg,
    # 添加完整的原始任务信息，用于重试
    "demand": sub_task_info.get('demand', ''),
    "module": sub_task_info.get('module', ''),
    "sub_task": sub_task_name,
    "workload": sub_task_info.get('workload', '')
}
```

### 2. 前端修复 - 重试数据转换

**修改文件**: `app/templates/result.html`

#### 批量重试功能
```javascript
// 重试失败的任务
$('#retryFailed').on('click', function() {
    if (!createResults || !createResults.failed || createResults.failed.length === 0) {
        alert('没有失败的任务需要重试');
        return;
    }
    
    if (confirm('确定要重试所有失败的任务吗？')) {
        // 将失败的任务转换为预览页面需要的格式
        const retryTasks = createResults.failed
            .filter(task => task.type === 'main_task' || task.type === 'sub_task')  // 只重试主任务和子任务
            .map(task => {
                return {
                    demand: task.demand || '',
                    module: task.module || '',
                    main_task: task.main_task || task.title,
                    sub_task: task.sub_task || (task.type === 'main_task' ? '' : task.title),
                    assignee: task.assignee || '',
                    workload: task.workload || '',
                    task_type: task.task_type === '主任务' ? 'UI' : task.task_type
                };
            });
        
        if (retryTasks.length === 0) {
            alert('没有可重试的任务（只能重试主任务和子任务）');
            return;
        }
        
        // 将转换后的任务存储到sessionStorage，然后跳转到预览页面
        sessionStorage.setItem('parsedTasks', JSON.stringify(retryTasks));
        window.location.href = '/preview';
    }
});
```

#### 单个任务重试功能
```javascript
// 重试单个任务
window.retryTask = function(task) {
    if (task.type !== 'main_task' && task.type !== 'sub_task') {
        alert('只能重试主任务和子任务');
        return;
    }
    
    if (confirm(`确定要重试任务"${task.title}"吗？`)) {
        // 将单个任务转换为预览页面需要的格式
        const retryTask = {
            demand: task.demand || '',
            module: task.module || '',
            main_task: task.main_task || task.title,
            sub_task: task.sub_task || (task.type === 'main_task' ? '' : task.title),
            assignee: task.assignee || '',
            workload: task.workload || '',
            task_type: task.task_type === '主任务' ? 'UI' : task.task_type
        };
        
        // 将单个任务存储到sessionStorage，然后跳转到预览页面
        sessionStorage.setItem('parsedTasks', JSON.stringify([retryTask]));
        window.location.href = '/preview';
    }
};
```

## 修复效果

### 1. 完整的任务信息传递 ✅

重试后的预览页面现在包含完整的任务信息：
- ✅ 需求（橙卡）
- ✅ 模块
- ✅ 主任务
- ✅ 子任务
- ✅ 负责人
- ✅ 工作量
- ✅ 任务类型

### 2. 智能任务过滤 ✅

- ✅ 只重试主任务和子任务
- ✅ 自动过滤不可重试的任务（如Sprint关联失败、需求关联失败）
- ✅ 提供清晰的提示信息

### 3. 数据格式转换 ✅

- ✅ 失败任务数据 → 预览页面格式
- ✅ 主任务类型转换（"主任务" → "UI"）
- ✅ 正确处理主任务和子任务的字段映射

## 测试验证

### 测试数据示例

**失败任务原始数据**:
```json
{
  "success": false,
  "type": "sub_task",
  "title": "web端 应用文案全部替换为fundx应用",
  "main_task": "MCP优化",
  "assignee": "关远",
  "task_type": "UI",
  "error": "主任务创建失败，子任务无法创建",
  "demand": "JGKEZH-23876",
  "module": "《MCP追加第四、五大点》",
  "sub_task": "web端 应用文案全部替换为fundx应用",
  "workload": "0.3"
}
```

**转换后的重试数据**:
```json
{
  "demand": "JGKEZH-23876",
  "module": "《MCP追加第四、五大点》",
  "main_task": "MCP优化",
  "sub_task": "web端 应用文案全部替换为fundx应用",
  "assignee": "关远",
  "workload": "0.3",
  "task_type": "UI"
}
```

### 测试结果

通过 `test_retry_functionality.py` 验证：
- ✅ 失败任务数据结构完整
- ✅ 数据转换逻辑正确
- ✅ 混合任务类型过滤正确
- ✅ 单个任务重试功能正常

## 使用方法

### 批量重试
1. 在结果页面点击"重试失败任务"按钮
2. 确认重试操作
3. 自动跳转到预览页面，显示完整的任务信息
4. 重新配置JIRA连接信息并创建任务

### 单个任务重试
1. 在失败任务列表中点击单个任务的"重试"按钮
2. 确认重试操作
3. 自动跳转到预览页面，显示该任务的完整信息
4. 重新配置JIRA连接信息并创建任务

## 注意事项

1. **只能重试主任务和子任务**: Sprint关联失败、需求关联失败等系统级错误不支持重试
2. **主任务类型转换**: 失败的主任务在重试时默认转换为UI类型，用户可在预览页面修改
3. **数据完整性**: 确保原始Excel数据包含完整的任务信息，否则重试时可能缺少某些字段
4. **会话存储**: 重试数据通过sessionStorage传递，刷新页面后数据会丢失

## 兼容性

- ✅ 兼容现有的Excel解析格式
- ✅ 兼容现有的预览页面逻辑
- ✅ 兼容现有的任务创建流程
- ✅ 向后兼容旧版本的失败任务数据
