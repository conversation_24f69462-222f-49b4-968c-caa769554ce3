# Jira任务批量创建工具

🚀 一个基于FastAPI的Web应用，支持通过Excel文件批量创建Jira任务，简化项目管理流程。

## 功能特性

- 📊 **Excel文件解析**: 支持.xlsx和.xls格式文件上传和解析
- 🔍 **任务预览**: 上传后可预览解析结果，确认无误后再创建
- 🎯 **批量创建**: 一键批量创建主任务和子任务
- 🔗 **需求关联**: 自动关联需求JIRA和Sprint
- 👥 **人员映射**: 自动转换中文姓名为英文用户名
- 📱 **响应式设计**: 支持桌面和移动设备访问
- 🎨 **现代UI**: 基于Bootstrap 5的美观界面

## 技术栈

- **后端**: FastAPI + Python 3.8+
- **前端**: Bootstrap 5 + jQuery
- **数据库**: MySQL 8.0+
- **文件处理**: pandas + openpyxl
- **Jira集成**: python-jira

## 快速开始

### 1. 环境要求

- Python 3.8+
- MySQL 8.0+
- pip

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd jira-task-creator

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境

```bash
# 复制环境配置文件
cp env.example .env

# 编辑配置文件
nano .env
```

### 4. 创建数据库

```sql
-- 创建数据库
CREATE DATABASE jira_tasks CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    oa_account VARCHAR(50) UNIQUE NOT NULL,
    chinese_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    department VARCHAR(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入示例数据
INSERT INTO users (oa_account, chinese_name, email, department) VALUES
('zhangsan', '张三', '<EMAIL>', '开发部'),
('lisi', '李四', '<EMAIL>', '测试部'),
('wangwu', '王五', '<EMAIL>', '产品部');
```

### 5. 启动应用

```bash
# 开发模式启动
python run.py

# 或者使用uvicorn直接启动
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 6. 访问应用

- 🌐 **主应用**: http://localhost:8000
- 📖 **API文档**: http://localhost:8000/docs
- 🔧 **ReDoc文档**: http://localhost:8000/redoc

## 使用说明

### Excel文件格式

Excel文件必须包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 橙卡 | 需求的JIRA Key | JGKEZH-23876 |
| 模块 | 功能模块名称 | 用户管理 |
| 主任务 | 主任务描述 | 实现用户登录功能 |
| 子任务 | 子任务描述 | 设计登录页面 |
| 任务类型 | UI/API/测试 | UI |
| 负责人 | 中文姓名 | 张三 |
| 工作量 | 预估工时 | 2天 |
| 迭代 | Sprint名称 | Sprint-2024-01 |

### 任务类型映射

- **UI** → 前端开发子任务 (ID: 11012)
- **API** → 后端开发子任务 (ID: 11013)  
- **测试** → 数据开发子任务 (ID: 11014)

### 操作流程

1. **上传Excel文件**: 选择符合格式要求的Excel文件
2. **预览任务列表**: 检查解析结果，确认任务信息
3. **配置Jira连接**: 输入Jira服务器地址和认证信息
4. **批量创建任务**: 一键创建所有任务
5. **查看创建结果**: 确认创建成功的任务

## API接口

### 解析Excel文件

```http
POST /api/v1/parse-excel
Content-Type: multipart/form-data

file: Excel文件
sheet_name: 工作表名称(可选)
```

### 创建Jira任务

```http
POST /api/v1/create-issues
Content-Type: application/json

{
    "tasks": [...],
    "jira_config": {
        "server": "https://your-jira-server.com",
        "username": "your_username",
        "password": "your_password",
        "project_key": "PROJECT"
    }
}
```

### 获取任务类型

```http
GET /api/v1/task-types
```

### 获取用户列表

```http
GET /api/v1/users
```

## 项目结构

```
jira-task-creator/
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── routers/           # 路由模块
│   │   ├── __init__.py
│   │   ├── api.py         # API路由
│   │   └── web.py         # Web页面路由
│   ├── static/            # 静态文件
│   │   ├── css/
│   │   │   └── style.css  # 自定义样式
│   │   └── js/
│   │       └── app.js     # 自定义脚本
│   └── templates/         # HTML模板
│       ├── base.html      # 基础模板
│       └── index.html     # 主页模板
├── requirements.txt       # Python依赖
├── run.py                # 启动脚本
├── env.example           # 环境配置示例
└── README.md             # 项目说明
```

## 开发计划

- [ ] 完成预览页面和结果页面
- [ ] 实现真实的Jira API集成
- [ ] 添加数据库连接和用户管理
- [ ] 实现Excel模板下载功能
- [ ] 添加任务创建历史记录
- [ ] 支持更多任务类型映射
- [ ] 添加批量操作和错误处理
- [ ] 实现Docker部署支持

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请提交 Issue 或联系项目维护者。

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！ 