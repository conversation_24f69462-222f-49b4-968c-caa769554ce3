// 全局JavaScript功能

// 页面加载完成后执行
$(document).ready(function() {
    // 初始化工具提示
    initTooltips();
    
    // 初始化文件拖拽上传
    initFileDragDrop();
    
    // 初始化表格功能
    initTableFeatures();
    
    // 初始化通用事件监听
    initEventListeners();
});

// 初始化工具提示
function initTooltips() {
    $('[data-bs-toggle="tooltip"]').tooltip();
}

// 初始化文件拖拽上传
function initFileDragDrop() {
    const fileInput = $('#excelFile');
    const uploadArea = $('.file-upload-area');
    
    if (fileInput.length && uploadArea.length) {
        // 阻止默认的拖拽行为
        $(document).on('dragenter dragover drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });
        
        // 拖拽进入
        uploadArea.on('dragenter dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('dragover');
        });
        
        // 拖拽离开
        uploadArea.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
        });
        
        // 文件拖拽放下
        uploadArea.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                fileInput[0].files = files;
                updateFileDisplay(files[0]);
            }
        });
    }
}

// 更新文件显示
function updateFileDisplay(file) {
    const fileName = file.name;
    const fileSize = formatFileSize(file.size);
    
    $('#fileInfo').html(`
        <div class="alert alert-info">
            <i class="fas fa-file-excel me-2"></i>
            已选择文件: <strong>${fileName}</strong> (${fileSize})
        </div>
    `);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 初始化表格功能
function initTableFeatures() {
    // 表格排序功能
    $('.sortable-table th[data-sort]').click(function() {
        const column = $(this).data('sort');
        const table = $(this).closest('table');
        const tbody = table.find('tbody');
        const rows = tbody.find('tr').toArray();
        
        const isAsc = $(this).hasClass('sort-asc');
        
        // 清除所有排序类
        table.find('th').removeClass('sort-asc sort-desc');
        
        // 添加当前排序类
        if (isAsc) {
            $(this).addClass('sort-desc');
        } else {
            $(this).addClass('sort-asc');
        }
        
        // 排序行
        rows.sort(function(a, b) {
            const aVal = $(a).find(`td[data-sort="${column}"]`).text().trim();
            const bVal = $(b).find(`td[data-sort="${column}"]`).text().trim();
            
            if (isAsc) {
                return bVal.localeCompare(aVal);
            } else {
                return aVal.localeCompare(bVal);
            }
        });
        
        // 重新排列表格行
        tbody.empty().append(rows);
    });
    
    // 表格行选择功能
    $('.selectable-table tbody').on('click', 'tr', function() {
        $(this).toggleClass('table-active');
        updateSelectedCount();
    });
    
    // 全选/取消全选
    $('.select-all-checkbox').change(function() {
        const isChecked = $(this).prop('checked');
        const table = $(this).closest('table');
        
        table.find('tbody input[type="checkbox"]').prop('checked', isChecked);
        table.find('tbody tr').toggleClass('table-active', isChecked);
        updateSelectedCount();
    });
}

// 更新选中数量
function updateSelectedCount() {
    const selectedCount = $('.selectable-table tbody tr.table-active').length;
    $('#selectedCount').text(selectedCount);
}

// 初始化通用事件监听
function initEventListeners() {
    // 表单验证
    $('form').on('submit', function(e) {
        const form = $(this);
        
        // 检查必填字段
        const requiredFields = form.find('[required]');
        let isValid = true;
        
        requiredFields.each(function() {
            const field = $(this);
            const value = field.val().trim();
            
            if (!value) {
                field.addClass('is-invalid');
                isValid = false;
            } else {
                field.removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            showNotification('请填写所有必填字段', 'error');
        }
    });
    
    // 输入框焦点事件
    $('.form-control').on('focus', function() {
        $(this).removeClass('is-invalid');
    });
    
    // 复制到剪贴板
    $('.copy-btn').click(function() {
        const text = $(this).data('copy');
        copyToClipboard(text);
        showNotification('已复制到剪贴板', 'success');
    });
}

// 显示通知消息
function showNotification(message, type = 'info', duration = 3000) {
    const alertClass = `alert-${type === 'error' ? 'danger' : type}`;
    const iconClass = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-triangle',
        'warning': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';
    
    const notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(notification);
    
    // 自动隐藏
    setTimeout(() => {
        notification.alert('close');
    }, duration);
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text);
    } else {
        // 备用方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('复制失败:', err);
        }
        
        document.body.removeChild(textArea);
    }
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 加载动画控制
function showLoading(element, text = '加载中...') {
    const spinner = `<span class="loading-spinner me-2"></span>${text}`;
    element.html(spinner).prop('disabled', true);
}

function hideLoading(element, originalText) {
    element.html(originalText).prop('disabled', false);
}

// 表格数据导出
function exportTableToCSV(tableId, filename = 'data.csv') {
    const table = document.getElementById(tableId);
    const rows = Array.from(table.querySelectorAll('tr'));
    
    const csvContent = rows.map(row => {
        const cells = Array.from(row.querySelectorAll('th, td'));
        return cells.map(cell => {
            const text = cell.textContent.trim();
            return `"${text.replace(/"/g, '""')}"`;
        }).join(',');
    }).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 本地存储工具
const LocalStorage = {
    set: function(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (e) {
            console.error('LocalStorage set error:', e);
        }
    },
    
    get: function(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (e) {
            console.error('LocalStorage get error:', e);
            return null;
        }
    },
    
    remove: function(key) {
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.error('LocalStorage remove error:', e);
        }
    }
};

// 会话存储工具
const SessionStorage = {
    set: function(key, value) {
        try {
            sessionStorage.setItem(key, JSON.stringify(value));
        } catch (e) {
            console.error('SessionStorage set error:', e);
        }
    },
    
    get: function(key) {
        try {
            const item = sessionStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (e) {
            console.error('SessionStorage get error:', e);
            return null;
        }
    },
    
    remove: function(key) {
        try {
            sessionStorage.removeItem(key);
        } catch (e) {
            console.error('SessionStorage remove error:', e);
        }
    }
}; 