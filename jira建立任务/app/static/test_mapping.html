<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>姓名映射测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>姓名映射功能测试</h2>
        
        <!-- 搜索测试 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>搜索功能测试</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="testSearchInput" class="form-label">搜索测试</label>
                    <input type="text" class="form-control" id="testSearchInput" placeholder="输入搜索词...">
                </div>
                <button type="button" class="btn btn-primary" onclick="testSearch()">测试搜索</button>
                <div id="searchResult" class="mt-3"></div>
            </div>
        </div>
        
        <!-- 添加映射测试 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>添加映射功能测试</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="testNameInput" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="testNameInput" placeholder="输入姓名...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="testLoginInput" class="form-label">OA账号</label>
                            <input type="text" class="form-control" id="testLoginInput" placeholder="输入OA账号...">
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-success" onclick="testAddMapping()">测试添加映射</button>
                <div id="addResult" class="mt-3"></div>
            </div>
        </div>
        
        <!-- 加载映射测试 -->
        <div class="card">
            <div class="card-header">
                <h5>加载映射功能测试</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-info" onclick="testLoadMappings()">测试加载映射</button>
                <div id="loadResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试搜索功能
        function testSearch() {
            const searchTerm = document.getElementById('testSearchInput').value.trim();
            const resultDiv = document.getElementById('searchResult');
            
            console.log('测试搜索，搜索词:', searchTerm);
            
            const searchUrl = '/api/v1/jira/name-mapping/search' + (searchTerm ? '?q=' + encodeURIComponent(searchTerm) : '');
            
            fetch(searchUrl)
                .then(response => {
                    console.log('搜索响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('搜索响应数据:', data);
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>搜索成功！</strong><br>
                                搜索词: "${data.search_term}"<br>
                                找到 ${data.count} 条记录<br>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>搜索失败！</strong><br>
                                ${data.message || '未知错误'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>搜索异常！</strong><br>
                            ${error.message}
                        </div>
                    `;
                });
        }
        
        // 测试添加映射功能
        function testAddMapping() {
            const name = document.getElementById('testNameInput').value.trim();
            const login = document.getElementById('testLoginInput').value.trim();
            const resultDiv = document.getElementById('addResult');
            
            if (!name || !login) {
                resultDiv.innerHTML = `
                    <div class="alert alert-warning">
                        请填写完整的姓名和OA账号
                    </div>
                `;
                return;
            }
            
            console.log('测试添加映射:', { name, login });
            
            fetch('/api/v1/jira/name-mapping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, login })
            })
                .then(response => {
                    console.log('添加响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('添加响应数据:', data);
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>添加成功！</strong><br>
                                ${data.message}
                            </div>
                        `;
                        // 清空输入框
                        document.getElementById('testNameInput').value = '';
                        document.getElementById('testLoginInput').value = '';
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>添加失败！</strong><br>
                                ${data.message || '未知错误'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('添加失败:', error);
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>添加异常！</strong><br>
                            ${error.message}
                        </div>
                    `;
                });
        }
        
        // 测试加载映射功能
        function testLoadMappings() {
            const resultDiv = document.getElementById('loadResult');
            
            console.log('测试加载映射');
            
            fetch('/api/v1/jira/name-mapping')
                .then(response => {
                    console.log('加载响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('加载响应数据:', data);
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>加载成功！</strong><br>
                                共 ${data.count} 条记录<br>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>加载失败！</strong><br>
                                ${data.message || '未知错误'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载失败:', error);
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>加载异常！</strong><br>
                            ${error.message}
                        </div>
                    `;
                });
        }
    </script>
</body>
</html>
