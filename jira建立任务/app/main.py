#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: main.py
描述: FastAPI应用主入口

更新日志:
2024-01-15 20:30 - 创建了FastAPI应用主入口
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import os

# 导入路由模块
from app.routers import web, api
from config.logging import setup_logging

# 初始化日志系统
setup_logging()

# 创建FastAPI应用实例
app = FastAPI(
    title="Jira任务批量创建工具",
    description="通过Excel文件批量创建Jira任务的Web应用",
    version="1.0.0"
)

# 配置静态文件服务
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# 配置模板引擎
templates = Jinja2Templates(directory="app/templates")

# 注册路由
app.include_router(web.router, tags=["Web页面"])
app.include_router(api.router, prefix="/api", tags=["API接口"])

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok", "message": "服务运行正常"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 