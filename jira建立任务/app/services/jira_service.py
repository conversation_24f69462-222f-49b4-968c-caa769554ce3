#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: jira_service.py
描述: JIRA服务模块

更新日志:
2024-01-15 - 创建JIRA服务模块
"""

import logging
from typing import Dict, List, Optional, Any
import requests
import json
from requests.auth import HTTPBasicAuth
from app.database import db_manager
from config.jira import jira_config

logger = logging.getLogger(__name__)

class JiraService:
    """JIRA服务类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def get_oa_login(self, name: str) -> Optional[str]:
        """根据姓名获取OA账号"""
        try:
            query = "SELECT login FROM t_jira_name_map WHERE name = :name LIMIT 1"
            result = db_manager.execute_query(query, {'name': name})
            
            if result and len(result) > 0:
                return result[0]['login']
            else:
                logger.warning(f"未找到姓名 '{name}' 对应的OA账号")
                return None
                
        except Exception as e:
            logger.error(f"查询OA账号失败: {str(e)}")
            return None
    
    def create_jira_issue(self, config: Dict[str, Any], task: Dict[str, Any]) -> Dict[str, Any]:
        """创建单个JIRA任务"""
        try:
            # 获取环境URL
            environment = config['environment']
            jira_url = jira_config.get_environment_url(environment)
            
            if not jira_url:
                raise Exception(f"无效的环境配置: {environment}")
            
            # 转换负责人姓名为OA账号
            assignee_name = task.get('assignee', '')
            assignee_oa = None
            if assignee_name:
                assignee_oa = self.get_oa_login(assignee_name)
                if not assignee_oa:
                    logger.warning(f"负责人 '{assignee_name}' 未找到对应的OA账号，将使用原始姓名")
            
            # 构建任务标题和描述
            title = task.get('sub_task') or task.get('main_task', '')
            description = self._build_task_description(task)
            
            # 构建JIRA任务数据
            issue_data = {
                "fields": {
                    "project": {
                        "key": config['project_key']
                    },
                    "summary": title,
                    "description": {
                        "type": "doc",
                        "version": 1,
                        "content": [
                            {
                                "type": "paragraph",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": description
                                    }
                                ]
                            }
                        ]
                    },
                    "issuetype": {
                        "name": "Task"  # 默认使用Task类型
                    }
                }
            }
            
            # 添加负责人（如果找到OA账号）
            if assignee_oa:
                issue_data["fields"]["assignee"] = {
                    "name": assignee_oa
                }
            
            # 添加Sprint信息（如果提供）
            sprint = config.get('sprint')
            if sprint:
                # 注意：Sprint字段的设置可能需要根据实际JIRA配置调整
                # 这里使用自定义字段的方式，具体字段ID需要根据实际情况调整
                pass  # Sprint设置需要知道具体的自定义字段ID
            
            # 发送创建请求
            create_url = f"{jira_url}/rest/api/2/issue"
            auth = HTTPBasicAuth(config['username'], config['token'])
            
            response = self.session.post(
                create_url,
                json=issue_data,
                auth=auth,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                issue_key = result['key']
                issue_url = f"{jira_url}/browse/{issue_key}"
                
                logger.info(f"成功创建JIRA任务: {issue_key}")
                
                return {
                    "success": True,
                    "key": issue_key,
                    "url": issue_url,
                    "title": title,
                    "assignee": assignee_name,
                    "assignee_oa": assignee_oa,
                    "task_type": task.get('task_type', ''),
                    "environment": environment,
                    "sprint": sprint
                }
            else:
                error_msg = f"JIRA API错误: {response.status_code}"
                try:
                    error_detail = response.json()
                    if 'errorMessages' in error_detail:
                        error_msg += f" - {', '.join(error_detail['errorMessages'])}"
                    elif 'errors' in error_detail:
                        error_msg += f" - {error_detail['errors']}"
                except:
                    error_msg += f" - {response.text}"
                
                logger.error(f"创建JIRA任务失败: {error_msg}")
                raise Exception(error_msg)
                
        except Exception as e:
            logger.error(f"创建JIRA任务异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "title": task.get('sub_task') or task.get('main_task', ''),
                "assignee": task.get('assignee', ''),
                "task_type": task.get('task_type', ''),
                "environment": config.get('environment', ''),
                "sprint": config.get('sprint', '')
            }
    
    def _build_task_description(self, task: Dict[str, Any]) -> str:
        """构建任务描述"""
        description_parts = []
        
        # 添加橙卡信息
        if task.get('demand'):
            description_parts.append(f"橙卡: {task['demand']}")
        
        # 添加模块信息
        if task.get('module'):
            description_parts.append(f"模块: {task['module']}")
        
        # 添加主任务信息
        if task.get('main_task'):
            description_parts.append(f"主任务: {task['main_task']}")
        
        # 添加工作量信息
        if task.get('workload'):
            description_parts.append(f"预估工作量: {task['workload']}d")
        
        # 添加任务类型
        if task.get('task_type'):
            description_parts.append(f"任务类型: {task['task_type']}")
        
        return "\n".join(description_parts) if description_parts else "无详细描述"
    
    def batch_create_issues(self, config: Dict[str, Any], tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量创建JIRA任务"""
        success_tasks = []
        failed_tasks = []
        
        logger.info(f"开始批量创建 {len(tasks)} 个JIRA任务")
        
        for i, task in enumerate(tasks):
            try:
                logger.info(f"创建任务 {i+1}/{len(tasks)}: {task.get('sub_task') or task.get('main_task', '')}")
                result = self.create_jira_issue(config, task)
                
                if result['success']:
                    success_tasks.append(result)
                else:
                    failed_tasks.append(result)
                    
            except Exception as e:
                logger.error(f"创建任务 {i+1} 时发生异常: {str(e)}")
                failed_tasks.append({
                    "success": False,
                    "error": str(e),
                    "title": task.get('sub_task') or task.get('main_task', ''),
                    "assignee": task.get('assignee', ''),
                    "task_type": task.get('task_type', ''),
                    "environment": config.get('environment', ''),
                    "sprint": config.get('sprint', '')
                })
        
        logger.info(f"批量创建完成: 成功 {len(success_tasks)} 个，失败 {len(failed_tasks)} 个")
        
        return {
            "success": len(success_tasks) > 0,
            "total": len(tasks),
            "success_count": len(success_tasks),
            "failed_count": len(failed_tasks),
            "success": success_tasks,
            "failed": failed_tasks
        }

# 全局服务实例
jira_service = JiraService()
