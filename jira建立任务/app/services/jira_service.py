#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: jira_service.py
描述: JIRA服务模块

更新日志:
2024-01-15 - 创建JIRA服务模块
"""

import logging
from typing import Dict, List, Optional, Any
import requests
import json
from app.database import db_manager
from config.jira import jira_config
from config.logging import get_jira_logger

logger = logging.getLogger(__name__)
jira_logger = get_jira_logger()

class JiraService:
    """JIRA服务类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def get_oa_login(self, name: str) -> Optional[str]:
        """根据姓名获取OA账号"""
        try:
            query = "SELECT login FROM t_jira_name_map WHERE name = :name LIMIT 1"
            result = db_manager.execute_query(query, {'name': name})
            
            if result and len(result) > 0:
                return result[0]['login']
            else:
                logger.warning(f"未找到姓名 '{name}' 对应的OA账号")
                return None
                
        except Exception as e:
            logger.error(f"查询OA账号失败: {str(e)}")
            return None
    
    def create_jira_issue(self, config: Dict[str, Any], task: Dict[str, Any]) -> Dict[str, Any]:
        """创建单个JIRA任务"""
        try:
            # 获取环境URL
            environment = config['environment']
            jira_url = jira_config.get_environment_url(environment)
            
            if not jira_url:
                raise Exception(f"无效的环境配置: {environment}")
            
            # 转换负责人姓名为OA账号
            assignee_name = task.get('assignee', '')
            assignee_oa = None
            if assignee_name:
                assignee_oa = self.get_oa_login(assignee_name)
                if not assignee_oa:
                    logger.warning(f"负责人 '{assignee_name}' 未找到对应的OA账号，将使用原始姓名")
            
            # 构建任务标题和描述
            title = task.get('sub_task') or task.get('main_task', '')
            description = self._build_task_description(task)
            
            # 构建JIRA任务数据
            issue_data = {
                "fields": {
                    "project": {
                        "key": config['project_key']
                    },
                    "summary": title,
                    "description": {
                        "type": "doc",
                        "version": 1,
                        "content": [
                            {
                                "type": "paragraph",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": description
                                    }
                                ]
                            }
                        ]
                    },
                    "issuetype": {
                        "name": "Task"  # 默认使用Task类型
                    }
                }
            }
            
            # 添加负责人（如果找到OA账号）
            if assignee_oa:
                issue_data["fields"]["assignee"] = {
                    "name": assignee_oa
                }
            
            # 添加Sprint信息（如果提供）
            sprint = config.get('sprint')
            if sprint:
                # 注意：Sprint字段的设置可能需要根据实际JIRA配置调整
                # 这里使用自定义字段的方式，具体字段ID需要根据实际情况调整
                pass  # Sprint设置需要知道具体的自定义字段ID
            
            # 发送创建请求
            create_url = f"{jira_url}/rest/api/latest/issue"
            headers = {
                'Authorization': f"Bearer {config['token']}",
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            # 记录请求详细信息
            jira_logger.info("=" * 80)
            jira_logger.info("JIRA API 请求详情:")
            jira_logger.info(f"请求URL: {create_url}")
            jira_logger.info(f"请求方法: POST")
            jira_logger.info(f"认证用户: {config['username']}")
            jira_logger.info(f"请求头: {dict(self.session.headers)}")
            jira_logger.info("请求体 (JSON):")
            jira_logger.info(json.dumps(issue_data, indent=2, ensure_ascii=False))
            jira_logger.info("=" * 80)

            response = self.session.post(
                create_url,
                json=issue_data,
                headers=headers,
                timeout=30
            )

            # 记录响应详细信息
            jira_logger.info("JIRA API 响应详情:")
            jira_logger.info(f"响应状态码: {response.status_code}")
            jira_logger.info(f"响应头: {dict(response.headers)}")
            jira_logger.info("响应体:")
            try:
                response_json = response.json()
                jira_logger.info(json.dumps(response_json, indent=2, ensure_ascii=False))
            except:
                jira_logger.info(f"响应文本: {response.text}")
            jira_logger.info("=" * 80)
            
            if response.status_code == 201:
                result = response.json()
                issue_key = result['key']
                issue_url = f"{jira_url}/browse/{issue_key}"
                
                logger.info(f"成功创建JIRA任务: {issue_key}")
                
                return {
                    "success": True,
                    "key": issue_key,
                    "url": issue_url,
                    "title": title,
                    "assignee": assignee_name,
                    "assignee_oa": assignee_oa,
                    "task_type": task.get('task_type', ''),
                    "environment": environment,
                    "sprint": sprint
                }
            else:
                error_msg = f"JIRA API错误: {response.status_code}"
                error_detail = None
                try:
                    error_detail = response.json()
                    if 'errorMessages' in error_detail:
                        error_msg += f" - {', '.join(error_detail['errorMessages'])}"
                    elif 'errors' in error_detail:
                        error_msg += f" - {error_detail['errors']}"
                except:
                    error_msg += f" - {response.text}"

                # 记录详细的错误信息
                logger.error("JIRA API 调用失败:")
                logger.error(f"状态码: {response.status_code}")
                logger.error(f"错误消息: {error_msg}")
                if error_detail:
                    logger.error("详细错误信息:")
                    logger.error(json.dumps(error_detail, indent=2, ensure_ascii=False))
                else:
                    logger.error(f"响应文本: {response.text}")

                raise Exception(error_msg)
                
        except Exception as e:
            logger.error(f"创建JIRA任务异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "title": task.get('sub_task') or task.get('main_task', ''),
                "assignee": task.get('assignee', ''),
                "task_type": task.get('task_type', ''),
                "environment": config.get('environment', ''),
                "sprint": config.get('sprint', '')
            }
    
    def _build_task_description(self, task: Dict[str, Any]) -> str:
        """构建任务描述"""
        description_parts = []
        
        # 添加橙卡信息
        if task.get('demand'):
            description_parts.append(f"橙卡: {task['demand']}")
        
        # 添加模块信息
        if task.get('module'):
            description_parts.append(f"模块: {task['module']}")
        
        # 添加主任务信息
        if task.get('main_task'):
            description_parts.append(f"主任务: {task['main_task']}")
        
        # 添加工作量信息
        if task.get('workload'):
            description_parts.append(f"预估工作量: {task['workload']}d")
        
        # 添加任务类型
        if task.get('task_type'):
            description_parts.append(f"任务类型: {task['task_type']}")
        
        return "\n".join(description_parts) if description_parts else "无详细描述"
    
    def test_jira_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试JIRA连接"""
        try:
            # 获取环境URL
            environment = config.get('environment', 'test')
            jira_url = jira_config.get_environment_url(environment)

            if not jira_url:
                raise Exception(f"无效的环境配置: {environment}")

            # 构建测试请求
            test_url = f"{jira_url}/rest/api/2/myself"
            headers = {
                'Authorization': f"Bearer {config['token']}",
                'Accept': 'application/json'
            }

            # 记录测试请求详细信息
            jira_logger.info("=" * 80)
            jira_logger.info("JIRA 连接测试详情:")
            jira_logger.info(f"测试URL: {test_url}")
            jira_logger.info(f"请求方法: GET")
            jira_logger.info(f"认证用户: {config['username']}")
            jira_logger.info(f"环境: {environment}")
            jira_logger.info("=" * 80)

            response = self.session.get(
                test_url,
                headers=headers,
                timeout=10
            )

            # 记录响应详细信息
            jira_logger.info("JIRA 连接测试响应:")
            jira_logger.info(f"响应状态码: {response.status_code}")
            jira_logger.info(f"响应头: {dict(response.headers)}")
            jira_logger.info("响应体:")
            try:
                response_json = response.json()
                jira_logger.info(json.dumps(response_json, indent=2, ensure_ascii=False))
            except:
                jira_logger.info(f"响应文本: {response.text}")
            jira_logger.info("=" * 80)

            if response.status_code == 200:
                user_info = response.json()
                logger.info(f"JIRA连接测试成功，用户: {user_info.get('displayName', config['username'])}")
                return {
                    "success": True,
                    "message": "连接测试成功",
                    "user_info": user_info
                }
            else:
                error_msg = f"连接测试失败: HTTP {response.status_code}"
                try:
                    error_detail = response.json()
                    if 'errorMessages' in error_detail:
                        error_msg += f" - {', '.join(error_detail['errorMessages'])}"
                except:
                    error_msg += f" - {response.text}"

                logger.error(f"JIRA连接测试失败: {error_msg}")
                return {
                    "success": False,
                    "message": error_msg
                }

        except Exception as e:
            logger.error(f"JIRA连接测试异常: {str(e)}")
            return {
                "success": False,
                "message": f"连接测试异常: {str(e)}"
            }

    def batch_create_issues_with_hierarchy(self, config: Dict[str, Any], tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        按照主任务-子任务层次结构批量创建JIRA任务

        执行步骤：
        1. 创建所有主任务，记录主任务的Jira key
        2. 按照表单的Sprint查找Sprint id，更新、关联到任务
        3. 将橙卡（需求）关联到主任务
        4. 创建主任务下的子任务
        """
        jira_logger.info("=" * 80)
        jira_logger.info("开始按层次结构批量创建JIRA任务")
        jira_logger.info(f"任务数量: {len(tasks)}")
        jira_logger.info(f"环境: {config.get('environment', 'unknown')}")
        jira_logger.info(f"项目: {config.get('project_key', 'unknown')}")
        jira_logger.info(f"用户: {config.get('username', 'unknown')}")
        jira_logger.info(f"Sprint: {config.get('sprint', 'unknown')}")
        jira_logger.info("=" * 80)

        # 分析任务结构，提取主任务和子任务的关系
        main_tasks_map, sub_tasks_list = self._analyze_task_hierarchy(tasks)

        jira_logger.info(f"分析结果: {len(main_tasks_map)} 个主任务, {len(sub_tasks_list)} 个子任务")

        # 结果统计
        results = {
            "success": True,
            "total": len(tasks),
            "main_tasks_created": 0,
            "sub_tasks_created": 0,
            "sprint_linked": 0,
            "demands_linked": 0,
            "success_tasks": [],
            "failed_tasks": []
        }

        try:
            # 步骤1: 创建所有主任务
            main_task_keys = self._create_main_tasks(config, main_tasks_map, results)

            # 步骤2: 查找Sprint ID并关联主任务
            if config.get('sprint') and main_task_keys:
                self._link_sprint_to_tasks(config, main_task_keys, results)

            # 步骤3: 将橙卡（需求）关联到主任务
            self._link_demands_to_main_tasks(config, main_tasks_map, main_task_keys, results)

            # 步骤4: 创建主任务下的子任务
            self._create_sub_tasks(config, sub_tasks_list, main_task_keys, results)

        except Exception as e:
            jira_logger.error(f"批量创建过程中发生异常: {str(e)}")
            results["success"] = False
            results["failed_tasks"].append({
                "success": False,
                "error": f"批量创建异常: {str(e)}",
                "type": "system_error"
            })

        # 记录最终结果
        jira_logger.info("=" * 80)
        jira_logger.info("JIRA 批量任务创建完成:")
        jira_logger.info(f"总任务数: {results['total']}")
        jira_logger.info(f"主任务创建: {results['main_tasks_created']} 个")
        jira_logger.info(f"子任务创建: {results['sub_tasks_created']} 个")
        jira_logger.info(f"Sprint关联: {results['sprint_linked']} 个")
        jira_logger.info(f"需求关联: {results['demands_linked']} 个")
        jira_logger.info(f"成功: {len(results['success_tasks'])} 个")
        jira_logger.info(f"失败: {len(results['failed_tasks'])} 个")
        jira_logger.info("=" * 80)

        return results

    def _analyze_task_hierarchy(self, tasks: List[Dict[str, Any]]) -> tuple:
        """
        分析任务层次结构，提取主任务和子任务的关系

        返回:
        - main_tasks_map: {main_task_name: main_task_info}
        - sub_tasks_list: [sub_task_info]
        """
        main_tasks_map = {}
        sub_tasks_list = []

        jira_logger.info("开始分析任务层次结构...")

        for i, task in enumerate(tasks):
            main_task_name = task.get('main_task', '').strip()
            sub_task_name = task.get('sub_task', '').strip()

            # 如果有主任务名称，记录主任务信息
            if main_task_name and main_task_name not in main_tasks_map:
                # 查找第一个包含此主任务的记录，获取完整信息
                main_task_info = {
                    'name': main_task_name,
                    'demand': task.get('demand', ''),
                    'module': task.get('module', ''),
                    'assignee': task.get('assignee', ''),
                    'workload': task.get('workload', ''),
                    'task_type': 'main_task',
                    'original_task': task
                }
                main_tasks_map[main_task_name] = main_task_info
                jira_logger.info(f"发现主任务: {main_task_name}")

            # 如果有子任务名称，记录子任务信息
            if sub_task_name:
                sub_task_info = {
                    'name': sub_task_name,
                    'main_task': main_task_name,
                    'assignee': task.get('assignee', ''),
                    'workload': task.get('workload', ''),
                    'task_type': task.get('task_type', ''),
                    'demand': task.get('demand', ''),
                    'module': task.get('module', ''),
                    'row_index': i,
                    'original_task': task
                }
                sub_tasks_list.append(sub_task_info)
                jira_logger.info(f"发现子任务: {sub_task_name} -> 主任务: {main_task_name}")

        jira_logger.info(f"任务结构分析完成: {len(main_tasks_map)} 个主任务, {len(sub_tasks_list)} 个子任务")

        return main_tasks_map, sub_tasks_list

    def _create_main_tasks(self, config: Dict[str, Any], main_tasks_map: Dict[str, Any], results: Dict[str, Any]) -> Dict[str, str]:
        """
        创建所有主任务

        返回: {main_task_name: jira_key}
        """
        main_task_keys = {}

        jira_logger.info("=" * 60)
        jira_logger.info("步骤1: 开始创建主任务")
        jira_logger.info(f"需要创建 {len(main_tasks_map)} 个主任务")
        jira_logger.info("=" * 60)

        for main_task_name, main_task_info in main_tasks_map.items():
            try:
                jira_logger.info(f"创建主任务: {main_task_name}")

                # 构建主任务数据
                main_task_data = self._build_main_task_data(config, main_task_info)

                # 调用JIRA API创建主任务
                jira_key = self._create_jira_issue_api(config, main_task_data, "main_task")

                if jira_key:
                    main_task_keys[main_task_name] = jira_key
                    results["main_tasks_created"] += 1

                    # 记录成功结果
                    success_result = {
                        "success": True,
                        "type": "main_task",
                        "key": jira_key,
                        "title": main_task_name,
                        "assignee": main_task_info.get('assignee', ''),
                        "url": f"{jira_config.get_environment_url(config['environment'])}/browse/{jira_key}"
                    }
                    results["success_tasks"].append(success_result)

                    jira_logger.info(f"主任务创建成功: {main_task_name} -> {jira_key}")
                else:
                    raise Exception("创建主任务失败，未返回JIRA Key")

            except Exception as e:
                error_msg = f"创建主任务失败: {str(e)}"
                jira_logger.error(f"主任务 '{main_task_name}' 创建失败: {error_msg}")

                # 记录失败结果
                failed_result = {
                    "success": False,
                    "type": "main_task",
                    "title": main_task_name,
                    "assignee": main_task_info.get('assignee', ''),
                    "error": error_msg
                }
                results["failed_tasks"].append(failed_result)

        jira_logger.info(f"主任务创建完成: 成功 {len(main_task_keys)} 个，失败 {len(main_tasks_map) - len(main_task_keys)} 个")
        return main_task_keys

    def _build_main_task_data(self, config: Dict[str, Any], main_task_info: Dict[str, Any]) -> Dict[str, Any]:
        """构建主任务的JIRA数据"""
        # 转换负责人姓名为OA账号
        assignee_name = main_task_info.get('assignee', '')
        assignee_oa = None
        if assignee_name:
            assignee_oa = self.get_oa_login(assignee_name)

        # 构建主任务描述
        description_parts = []
        if main_task_info.get('demand'):
            description_parts.append(f"橙卡: {main_task_info['demand']}")
        if main_task_info.get('module'):
            description_parts.append(f"模块: {main_task_info['module']}")
        if main_task_info.get('workload'):
            description_parts.append(f"预估工作量: {main_task_info['workload']}d")

        description = "\n".join(description_parts) if description_parts else "主任务"

        # 构建主任务数据（迭代功能类型）
        issue_data = {
            "fields": {
                "project": {
                    "key": config['project_key']
                },
                "summary": main_task_info['name'],
                "description": description,  # 使用简单的文本格式
                "issuetype": {
                    "id": "11007"  # 迭代功能类型
                },
                "priority": {
                    "id": "3"  # 中等优先级
                },
                "reporter": {
                    "name": config['username']
                }
            }
        }

        # 添加负责人（如果找到OA账号）
        if assignee_oa:
            issue_data["fields"]["assignee"] = {"name": assignee_oa}
            # 开发责任人
            issue_data["fields"]["customfield_13103"] = {"name": assignee_oa}

        # 测试责任人（从配置中获取，默认为zhouqishu）
        test_assignee = config.get('test_assignee', 'zhouqishu')
        if test_assignee:
            issue_data["fields"]["customfield_11305"] = {"name": test_assignee}

        return issue_data

    def _create_jira_issue_api(self, config: Dict[str, Any], issue_data: Dict[str, Any], task_type: str) -> Optional[str]:
        """
        调用JIRA API创建任务

        返回: JIRA Key 或 None
        """
        try:
            # 获取环境URL
            environment = config['environment']
            jira_url = jira_config.get_environment_url(environment)

            if not jira_url:
                raise Exception(f"无效的环境配置: {environment}")

            # 发送创建请求
            create_url = f"{jira_url}/rest/api/latest/issue"
            headers = {
                'Authorization': f"Bearer {config['token']}",
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            # 记录请求详细信息
            jira_logger.info("=" * 80)
            jira_logger.info(f"JIRA API 创建{task_type}请求详情:")
            jira_logger.info(f"请求URL: {create_url}")
            jira_logger.info(f"请求方法: POST")
            jira_logger.info(f"认证用户: {config['username']}")
            jira_logger.info("请求体 (JSON):")
            jira_logger.info(json.dumps(issue_data, indent=2, ensure_ascii=False))
            jira_logger.info("=" * 80)

            response = self.session.post(
                create_url,
                json=issue_data,
                headers=headers,
                timeout=30
            )

            # 记录响应详细信息
            jira_logger.info(f"JIRA API 创建{task_type}响应详情:")
            jira_logger.info(f"响应状态码: {response.status_code}")
            jira_logger.info(f"响应头: {dict(response.headers)}")
            jira_logger.info("响应体:")
            try:
                response_json = response.json()
                jira_logger.info(json.dumps(response_json, indent=2, ensure_ascii=False))
            except:
                jira_logger.info(f"响应文本: {response.text}")
            jira_logger.info("=" * 80)

            if response.status_code == 201:
                result = response.json()
                jira_key = result['key']
                jira_logger.info(f"成功创建{task_type}: {jira_key}")
                return jira_key
            else:
                error_msg = f"JIRA API错误: {response.status_code}"
                try:
                    error_detail = response.json()
                    if 'errorMessages' in error_detail:
                        error_msg += f" - {', '.join(error_detail['errorMessages'])}"
                    elif 'errors' in error_detail:
                        error_msg += f" - {error_detail['errors']}"
                except:
                    error_msg += f" - {response.text}"

                jira_logger.error(f"创建{task_type}失败: {error_msg}")
                raise Exception(error_msg)

        except Exception as e:
            jira_logger.error(f"创建{task_type}异常: {str(e)}")
            raise e

    def _link_sprint_to_tasks(self, config: Dict[str, Any], main_task_keys: Dict[str, str], results: Dict[str, Any]):
        """
        步骤2: 查找Sprint ID并关联主任务
        """
        sprint_name = config.get('sprint', '').strip()
        if not sprint_name:
            jira_logger.info("未提供Sprint名称，跳过Sprint关联")
            return

        jira_logger.info("=" * 60)
        jira_logger.info("步骤2: 开始Sprint关联")
        jira_logger.info(f"Sprint名称: {sprint_name}")
        jira_logger.info(f"需要关联 {len(main_task_keys)} 个主任务")
        jira_logger.info("=" * 60)

        try:
            # 查找Sprint ID
            sprint_id = self._find_sprint_id(config, sprint_name)

            if sprint_id > 0:
                jira_logger.info(f"找到Sprint ID: {sprint_id}")

                # 关联所有主任务到Sprint
                for main_task_name, jira_key in main_task_keys.items():
                    try:
                        self._link_task_to_sprint(config, jira_key, sprint_id)
                        results["sprint_linked"] += 1
                        jira_logger.info(f"任务 {jira_key} 成功关联到Sprint {sprint_name}")

                    except Exception as e:
                        error_msg = f"任务 {jira_key} Sprint关联失败: {str(e)}"
                        jira_logger.error(error_msg)

                        # 记录失败结果
                        failed_result = {
                            "success": False,
                            "type": "sprint_link",
                            "title": f"Sprint关联 - {main_task_name}",
                            "key": jira_key,
                            "error": error_msg
                        }
                        results["failed_tasks"].append(failed_result)
            else:
                error_msg = f"未找到Sprint: {sprint_name}"
                jira_logger.error(error_msg)

                # 记录失败结果
                failed_result = {
                    "success": False,
                    "type": "sprint_link",
                    "title": "Sprint关联",
                    "error": error_msg
                }
                results["failed_tasks"].append(failed_result)

        except Exception as e:
            error_msg = f"Sprint关联过程异常: {str(e)}"
            jira_logger.error(error_msg)

            # 记录失败结果
            failed_result = {
                "success": False,
                "type": "sprint_link",
                "title": "Sprint关联",
                "error": error_msg
            }
            results["failed_tasks"].append(failed_result)

        jira_logger.info(f"Sprint关联完成: 成功 {results['sprint_linked']} 个")

    def _find_sprint_id(self, config: Dict[str, Any], sprint_name: str) -> int:
        """查找Sprint ID"""
        try:
            # 获取环境URL
            environment = config['environment']
            jira_url = jira_config.get_environment_url(environment)

            # 查询Sprint ID
            sprint_url = f"{jira_url}/rest/greenhopper/1.0/sprint/picker?query={sprint_name}"
            headers = {
                'Authorization': f"Bearer {config['token']}",
                'Accept': 'application/json'
            }

            jira_logger.info(f"查询Sprint ID: {sprint_url}")

            response = self.session.get(sprint_url, headers=headers, timeout=10)

            jira_logger.info(f"Sprint查询响应状态码: {response.status_code}")
            jira_logger.info(f"Sprint查询响应: {response.text}")

            if response.status_code == 200:
                resp_data = response.json()

                # 优先从suggestions中查找
                if resp_data.get('suggestions') and len(resp_data['suggestions']) >= 1:
                    sprint_id = resp_data['suggestions'][0]['id']
                    jira_logger.info(f"从suggestions找到Sprint ID: {sprint_id}")
                    return sprint_id

                # 从allMatches中查找
                elif resp_data.get('allMatches') and len(resp_data['allMatches']) >= 1:
                    sprint_id = resp_data['allMatches'][0]['id']
                    jira_logger.info(f"从allMatches找到Sprint ID: {sprint_id}")
                    return sprint_id

                else:
                    jira_logger.error(f"未找到Sprint: {sprint_name}")
                    return -1
            else:
                jira_logger.error(f"Sprint查询失败: HTTP {response.status_code}")
                return -1

        except Exception as e:
            jira_logger.error(f"查找Sprint ID异常: {str(e)}")
            return -1

    def _link_task_to_sprint(self, config: Dict[str, Any], jira_key: str, sprint_id: int):
        """将任务关联到Sprint"""
        try:
            # 获取环境URL
            environment = config['environment']
            jira_url = jira_config.get_environment_url(environment)

            # 更新任务的Sprint字段
            issue_url = f"{jira_url}/rest/api/latest/issue/{jira_key}"
            headers = {
                'Authorization': f"Bearer {config['token']}",
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            payload = {
                'fields': {
                    'customfield_10005': sprint_id  # Sprint字段
                }
            }

            jira_logger.info(f"关联任务 {jira_key} 到Sprint {sprint_id}")
            jira_logger.info(f"请求URL: {issue_url}")
            jira_logger.info(f"请求体: {json.dumps(payload, indent=2)}")

            response = self.session.put(
                issue_url,
                json=payload,
                headers=headers,
                timeout=10
            )

            jira_logger.info(f"Sprint关联响应状态码: {response.status_code}")
            jira_logger.info(f"Sprint关联响应: {response.text}")

            if response.status_code != 204:
                raise Exception(f"Sprint关联失败: HTTP {response.status_code} - {response.text}")

        except Exception as e:
            jira_logger.error(f"任务 {jira_key} Sprint关联异常: {str(e)}")
            raise e

    def _link_demands_to_main_tasks(self, config: Dict[str, Any], main_tasks_map: Dict[str, Any],
                                   main_task_keys: Dict[str, str], results: Dict[str, Any]):
        """
        步骤3: 将橙卡（需求）关联到主任务
        """
        jira_logger.info("=" * 60)
        jira_logger.info("步骤3: 开始需求关联")
        jira_logger.info("=" * 60)

        for main_task_name, main_task_info in main_tasks_map.items():
            demand = main_task_info.get('demand', '').strip()
            jira_key = main_task_keys.get(main_task_name)

            if not demand or not jira_key:
                if not demand:
                    jira_logger.info(f"主任务 {main_task_name} 无需求信息，跳过需求关联")
                continue

            try:
                jira_logger.info(f"关联需求 {demand} 到主任务 {jira_key}")
                self._link_demand_to_task(config, jira_key, demand)
                results["demands_linked"] += 1
                jira_logger.info(f"需求 {demand} 成功关联到任务 {jira_key}")

            except Exception as e:
                error_msg = f"需求 {demand} 关联失败: {str(e)}"
                jira_logger.error(error_msg)

                # 记录失败结果
                failed_result = {
                    "success": False,
                    "type": "demand_link",
                    "title": f"需求关联 - {main_task_name}",
                    "key": jira_key,
                    "demand": demand,
                    "error": error_msg
                }
                results["failed_tasks"].append(failed_result)

        jira_logger.info(f"需求关联完成: 成功 {results['demands_linked']} 个")

    def _link_demand_to_task(self, config: Dict[str, Any], jira_key: str, demand_key: str):
        """将需求关联到任务"""
        try:
            # 获取环境URL
            environment = config['environment']
            jira_url = jira_config.get_environment_url(environment)

            # 更新任务，关联需求JIRA
            issue_url = f"{jira_url}/rest/api/latest/issue/{jira_key}"
            headers = {
                'Authorization': f"Bearer {config['token']}",
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            payload = {
                'update': {
                    'issuelinks': [
                        {
                            'add': {
                                'type': {
                                    'name': '需求关联',
                                    'inward': '需求关联',
                                    'outward': '需求关联'
                                },
                                'inwardIssue': {
                                    'key': demand_key
                                }
                            }
                        }
                    ]
                }
            }

            jira_logger.info(f"关联需求 {demand_key} 到任务 {jira_key}")
            jira_logger.info(f"请求URL: {issue_url}")
            jira_logger.info(f"请求体: {json.dumps(payload, indent=2, ensure_ascii=False)}")

            response = self.session.put(
                issue_url,
                json=payload,
                headers=headers,
                timeout=10
            )

            jira_logger.info(f"需求关联响应状态码: {response.status_code}")
            jira_logger.info(f"需求关联响应: {response.text}")

            if response.status_code != 204:
                raise Exception(f"需求关联失败: HTTP {response.status_code} - {response.text}")

        except Exception as e:
            jira_logger.error(f"需求 {demand_key} 关联到任务 {jira_key} 异常: {str(e)}")
            raise e

    def _create_sub_tasks(self, config: Dict[str, Any], sub_tasks_list: List[Dict[str, Any]],
                         main_task_keys: Dict[str, str], results: Dict[str, Any]):
        """
        步骤4: 创建主任务下的子任务
        注意：如果主任务没有创建成功，子任务的创建直接认定为失败
        """
        jira_logger.info("=" * 60)
        jira_logger.info("步骤4: 开始创建子任务")
        jira_logger.info(f"需要创建 {len(sub_tasks_list)} 个子任务")
        jira_logger.info(f"成功创建的主任务数量: {len(main_task_keys)}")
        jira_logger.info("=" * 60)

        # 任务类型映射
        task_type_mapping = {
            'UI': '11012',      # 前端开发子任务
            'API': '11013',     # 后端开发子任务
            '测试': '11014'      # 数据开发子任务
        }

        # 预先检查并标记失败的子任务（主任务创建失败的情况）
        valid_sub_tasks = []
        failed_sub_tasks = []

        for sub_task_info in sub_tasks_list:
            sub_task_name = sub_task_info['name']
            main_task_name = sub_task_info['main_task']
            parent_key = main_task_keys.get(main_task_name)

            if not parent_key:
                error_msg = f"主任务 '{main_task_name}' 创建失败，子任务 '{sub_task_name}' 无法创建"
                jira_logger.error(error_msg)

                # 记录失败结果
                failed_result = {
                    "success": False,
                    "type": "sub_task",
                    "title": sub_task_name,
                    "main_task": main_task_name,
                    "assignee": sub_task_info.get('assignee', ''),
                    "task_type": sub_task_info.get('task_type', ''),
                    "error": error_msg
                }
                results["failed_tasks"].append(failed_result)
                failed_sub_tasks.append(sub_task_info)
            else:
                valid_sub_tasks.append(sub_task_info)

        jira_logger.info(f"有效子任务: {len(valid_sub_tasks)} 个")
        jira_logger.info(f"因主任务失败而跳过的子任务: {len(failed_sub_tasks)} 个")

        # 只创建有效的子任务
        for i, sub_task_info in enumerate(valid_sub_tasks):
            try:
                sub_task_name = sub_task_info['name']
                main_task_name = sub_task_info['main_task']
                parent_key = main_task_keys[main_task_name]  # 这里可以安全地使用，因为已经验证过

                jira_logger.info(f"创建子任务 {i+1}/{len(valid_sub_tasks)}: {sub_task_name} -> 主任务: {parent_key}")

                # 构建子任务数据
                sub_task_data = self._build_sub_task_data(config, sub_task_info, parent_key, task_type_mapping)

                # 调用JIRA API创建子任务
                jira_key = self._create_jira_issue_api(config, sub_task_data, "sub_task")

                if jira_key:
                    results["sub_tasks_created"] += 1

                    # 记录成功结果
                    success_result = {
                        "success": True,
                        "type": "sub_task",
                        "key": jira_key,
                        "title": sub_task_name,
                        "parent_key": parent_key,
                        "assignee": sub_task_info.get('assignee', ''),
                        "task_type": sub_task_info.get('task_type', ''),
                        "url": f"{jira_config.get_environment_url(config['environment'])}/browse/{jira_key}"
                    }
                    results["success_tasks"].append(success_result)

                    jira_logger.info(f"子任务创建成功: {sub_task_name} -> {jira_key}")
                else:
                    raise Exception("创建子任务失败，未返回JIRA Key")

            except Exception as e:
                error_msg = f"创建子任务失败: {str(e)}"
                jira_logger.error(f"子任务 '{sub_task_info['name']}' 创建失败: {error_msg}")

                # 记录失败结果
                failed_result = {
                    "success": False,
                    "type": "sub_task",
                    "title": sub_task_info['name'],
                    "main_task": sub_task_info['main_task'],
                    "assignee": sub_task_info.get('assignee', ''),
                    "task_type": sub_task_info.get('task_type', ''),
                    "error": error_msg
                }
                results["failed_tasks"].append(failed_result)

        total_failed_sub_tasks = len(failed_sub_tasks) + (len(valid_sub_tasks) - results['sub_tasks_created'])
        jira_logger.info(f"子任务创建完成: 成功 {results['sub_tasks_created']} 个，失败 {total_failed_sub_tasks} 个")

    def _build_sub_task_data(self, config: Dict[str, Any], sub_task_info: Dict[str, Any],
                           parent_key: str, task_type_mapping: Dict[str, str]) -> Dict[str, Any]:
        """构建子任务的JIRA数据"""
        # 转换负责人姓名为OA账号
        assignee_name = sub_task_info.get('assignee', '')
        assignee_oa = None
        if assignee_name:
            assignee_oa = self.get_oa_login(assignee_name)

        # 构建子任务描述
        description_parts = []
        if sub_task_info.get('demand'):
            description_parts.append(f"橙卡: {sub_task_info['demand']}")
        if sub_task_info.get('module'):
            description_parts.append(f"模块: {sub_task_info['module']}")
        if sub_task_info.get('workload'):
            description_parts.append(f"预估工作量: {sub_task_info['workload']}d")
        if sub_task_info.get('task_type'):
            description_parts.append(f"任务类型: {sub_task_info['task_type']}")

        description = "\n".join(description_parts) if description_parts else "子任务"

        # 获取任务类型ID
        task_type = sub_task_info.get('task_type', '')
        task_type_id = task_type_mapping.get(task_type, '11012')  # 默认为前端开发子任务

        # 构建子任务数据
        issue_data = {
            "fields": {
                "project": {
                    "key": config['project_key']
                },
                "summary": sub_task_info['name'],
                "description": description,  # 使用简单的文本格式
                "issuetype": {
                    "id": task_type_id
                },
                "priority": {
                    "id": "3"  # 中等优先级
                },
                "parent": {
                    "key": parent_key  # 关联主任务
                },
                "reporter": {
                    "name": config['username']
                }
            }
        }

        # 添加负责人（如果找到OA账号）
        if assignee_oa:
            issue_data["fields"]["assignee"] = {"name": assignee_oa}

        # 添加预估耗时
        workload = sub_task_info.get('workload', '')
        if workload:
            try:
                # 将工作量转换为小时（假设1天=8小时）
                workload_float = float(workload)
                hours = int(workload_float * 8)
                time_estimate = f"{hours}h"

                issue_data["fields"]["timetracking"] = {
                    "originalEstimate": time_estimate,
                    "remainingEstimate": time_estimate
                }
            except (ValueError, TypeError):
                jira_logger.warning(f"无效的工作量格式: {workload}")

        return issue_data

    def batch_create_issues(self, config: Dict[str, Any], tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量创建JIRA任务 - 兼容旧版本接口"""
        return self.batch_create_issues_with_hierarchy(config, tasks)

# 全局服务实例
jira_service = JiraService()
