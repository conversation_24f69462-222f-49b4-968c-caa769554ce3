{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="display-6">
                <i class="fas fa-check-circle text-success me-2"></i>
                创建结果
            </h1>
            <p class="lead text-muted">Jira任务批量创建完成</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="step-indicator completed">
                    <div class="step-number">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="step-title">上传文件</div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="step-indicator completed">
                    <div class="step-number">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="step-title">预览任务</div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="step-indicator completed">
                    <div class="step-number">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="step-title">创建完成</div>
                </div>
            </div>
        </div>

        <!-- 创建结果统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <h5 class="card-title text-success">
                            <i class="fas fa-check-circle"></i>
                        </h5>
                        <h3 id="successCount" class="mb-0 text-success">0</h3>
                        <p class="card-text text-muted">成功创建</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-danger">
                    <div class="card-body">
                        <h5 class="card-title text-danger">
                            <i class="fas fa-times-circle"></i>
                        </h5>
                        <h3 id="failedCount" class="mb-0 text-danger">0</h3>
                        <p class="card-text text-muted">创建失败</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-info">
                    <div class="card-body">
                        <h5 class="card-title text-info">
                            <i class="fas fa-tasks"></i>
                        </h5>
                        <h3 id="totalCount" class="mb-0 text-info">0</h3>
                        <p class="card-text text-muted">总任务数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-primary">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-percentage"></i>
                        </h5>
                        <h3 id="successRate" class="mb-0 text-primary">0%</h3>
                        <p class="card-text text-muted">成功率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成功创建的任务 -->
        <div class="card mb-4" id="successCard" style="display: none;">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    成功创建的任务
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>任务标题</th>
                                <th>任务类型</th>
                                <th>负责人</th>
                                <th>Jira Key</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="successTableBody">
                            <!-- 成功的任务将在这里显示 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 失败的任务 -->
        <div class="card mb-4" id="failedCard" style="display: none;">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-times-circle me-2"></i>
                    创建失败的任务
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>任务标题</th>
                                <th>任务类型</th>
                                <th>负责人</th>
                                <th>失败原因</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="failedTableBody">
                            <!-- 失败的任务将在这里显示 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="d-flex justify-content-between">
            <div>
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    创建新任务
                </a>
                <button type="button" class="btn btn-outline-secondary" id="exportResults">
                    <i class="fas fa-download me-1"></i>
                    导出结果
                </button>
            </div>
            <div>
                <button type="button" class="btn btn-outline-danger" id="retryFailed" style="display: none;">
                    <i class="fas fa-redo me-1"></i>
                    重试失败任务
                </button>
            </div>
        </div>

        <!-- 导出结果模态框 -->
        <div class="modal fade" id="exportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-download me-2"></i>
                            导出创建结果
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>选择要导出的内容：</p>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportSuccess" checked>
                            <label class="form-check-label" for="exportSuccess">
                                成功创建的任务
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportFailed" checked>
                            <label class="form-check-label" for="exportFailed">
                                创建失败的任务
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportSummary" checked>
                            <label class="form-check-label" for="exportSummary">
                                创建结果摘要
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmExport">
                            <i class="fas fa-download me-1"></i>
                            导出Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    let createResults = null;
    let jiraEnvironments = {};

    // 加载JIRA环境配置
    function loadJiraEnvironments() {
        return $.get('/api/v1/jira/environments')
            .done(function(response) {
                if (response.success) {
                    jiraEnvironments = response.data;
                }
            })
            .fail(function(xhr) {
                console.error('加载JIRA环境配置失败:', xhr);
            });
    }

    // 根据环境和KEY生成JIRA链接
    function generateJiraUrl(environment, key) {
        if (!environment || !key || !jiraEnvironments[environment]) {
            return '#';
        }
        const baseUrl = jiraEnvironments[environment].url;
        return `${baseUrl}/browse/${key}`;
    }

    // 从sessionStorage加载创建结果
    function loadCreateResults() {
        const resultsData = sessionStorage.getItem('createResults');
        if (!resultsData) {
            // 如果没有数据，重定向到首页
            window.location.href = '/';
            return;
        }
        
        try {
            createResults = JSON.parse(resultsData);
            displayResults();
        } catch (error) {
            console.error('解析创建结果失败:', error);
            alert('数据格式错误，请重新创建任务');
            window.location.href = '/';
        }
    }
    
    // 显示创建结果
    function displayResults() {
        if (!createResults) return;
        
        const successTasks = createResults.success || [];
        const failedTasks = createResults.failed || [];
        const totalTasks = successTasks.length + failedTasks.length;
        const successRate = totalTasks > 0 ? Math.round((successTasks.length / totalTasks) * 100) : 0;
        
        // 更新统计信息
        $('#successCount').text(successTasks.length);
        $('#failedCount').text(failedTasks.length);
        $('#totalCount').text(totalTasks);
        $('#successRate').text(successRate + '%');
        
        // 显示成功的任务
        if (successTasks.length > 0) {
            $('#successCard').show();
            displaySuccessTasks(successTasks);
        }
        
        // 显示失败的任务
        if (failedTasks.length > 0) {
            $('#failedCard').show();
            $('#retryFailed').show();
            displayFailedTasks(failedTasks);
        }
    }
    
    // 显示成功的任务
    function displaySuccessTasks(tasks) {
        const tbody = $('#successTableBody');
        tbody.empty();

        tasks.forEach(task => {
            // 生成JIRA链接
            const jiraUrl = task.url || generateJiraUrl(task.environment, task.key);

            const row = `
                <tr>
                    <td>${task.title || task.summary || ''}</td>
                    <td>
                        <span class="badge ${getTaskTypeBadgeClass(task.task_type)}">
                            ${task.task_type || ''}
                        </span>
                    </td>
                    <td>${task.assignee || ''}</td>
                    <td>
                        <a href="${jiraUrl}" target="_blank" class="text-decoration-none">
                            <strong>${task.key || ''}</strong>
                            <i class="fas fa-external-link-alt ms-1"></i>
                        </a>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-primary"
                                onclick="copyToClipboard('${task.key || ''}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }
    
    // 显示失败的任务
    function displayFailedTasks(tasks) {
        const tbody = $('#failedTableBody');
        tbody.empty();
        
        tasks.forEach(task => {
            const row = `
                <tr>
                    <td>${task.title || task.summary || ''}</td>
                    <td>
                        <span class="badge ${getTaskTypeBadgeClass(task.task_type)}">
                            ${task.task_type || ''}
                        </span>
                    </td>
                    <td>${task.assignee || ''}</td>
                    <td>
                        <span class="text-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            ${task.error || '未知错误'}
                        </span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                onclick="retryTask(${JSON.stringify(task).replace(/"/g, '&quot;')})">
                            <i class="fas fa-redo"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }
    
    // 获取任务类型对应的badge样式
    function getTaskTypeBadgeClass(taskType) {
        switch (taskType) {
            case 'API': return 'bg-success';
            case 'UI': return 'bg-info';
            case '测试': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }
    
    // 复制到剪贴板
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(function() {
            // 显示成功提示
            const toast = `
                <div class="toast align-items-center text-white bg-success border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            已复制到剪贴板: ${text}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            $('body').append(toast);
            $('.toast').last().toast('show');
        });
    };
    
    // 重试单个任务
    window.retryTask = function(task) {
        // 这里可以实现重试单个任务的逻辑
        alert('重试功能开发中...');
    };
    
    // 导出结果
    $('#exportResults').on('click', function() {
        $('#exportModal').modal('show');
    });
    
    $('#confirmExport').on('click', function() {
        const includeSuccess = $('#exportSuccess').prop('checked');
        const includeFailed = $('#exportFailed').prop('checked');
        const includeSummary = $('#exportSummary').prop('checked');
        
        // 发送导出请求
        $.ajax({
            url: '/api/v1/export-results',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                results: createResults,
                include_success: includeSuccess,
                include_failed: includeFailed,
                include_summary: includeSummary
            }),
            success: function(response) {
                if (response.success) {
                    // 下载文件
                    const link = document.createElement('a');
                    link.href = response.download_url;
                    link.download = response.filename;
                    link.click();
                    
                    $('#exportModal').modal('hide');
                } else {
                    alert('导出失败: ' + response.message);
                }
            },
            error: function(xhr) {
                let errorMessage = '导出失败';
                if (xhr.responseJSON && xhr.responseJSON.detail) {
                    errorMessage = xhr.responseJSON.detail;
                }
                alert(errorMessage);
            }
        });
    });
    
    // 重试失败的任务
    $('#retryFailed').on('click', function() {
        if (!createResults || !createResults.failed || createResults.failed.length === 0) {
            alert('没有失败的任务需要重试');
            return;
        }
        
        if (confirm('确定要重试所有失败的任务吗？')) {
            // 将失败的任务存储到sessionStorage，然后跳转到预览页面
            sessionStorage.setItem('parsedTasks', JSON.stringify(createResults.failed));
            window.location.href = '/preview';
        }
    });
    
    // 页面加载时执行
    loadJiraEnvironments().always(function() {
        loadCreateResults();
    });
});
</script>
{% endblock %}
