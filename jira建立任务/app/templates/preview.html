{% extends "base.html" %}

{% block content %}
<style>
/* 任务层次结构样式 - 简化版本 */
.task-hierarchy {
    border-left: 2px solid transparent;
}

.task-hierarchy.level-0 {
    border-left-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.task-hierarchy.level-1 {
    border-left-color: #0dcaf0;
    background-color: rgba(13, 202, 240, 0.05);
}

.task-hierarchy.level-2 {
    background-color: rgba(248, 249, 250, 0.8);
}

.task-hierarchy.level-3 {
    background-color: #ffffff;
}

/* 合并单元格效果 */
.merged-cell {
    border-top: none !important;
    color: #6c757d;
    font-style: italic;
}

/* 子任务缩进样式 - 简化版本 */
.sub-task-indent {
    padding-left: 1rem;
    color: #6c757d;
}

/* 分组标题样式 - 简化版本 */
.group-header {
    font-weight: 500;
}
</style>
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="display-6">
                <i class="fas fa-eye text-primary me-2"></i>
                任务预览
            </h1>
            <p class="lead text-muted">确认任务信息后，点击创建按钮批量创建Jira任务</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="step-indicator completed">
                    <div class="step-number">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="step-title">上传文件</div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="step-indicator active">
                    <div class="step-number">2</div>
                    <div class="step-title">预览任务</div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="step-indicator">
                    <div class="step-number">3</div>
                    <div class="step-title">创建完成</div>
                </div>
            </div>
        </div>

        <!-- 任务统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-tasks"></i>
                        </h5>
                        <h3 id="totalTasks" class="mb-0">0</h3>
                        <p class="card-text text-muted">总任务数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">
                            <i class="fas fa-code"></i>
                        </h5>
                        <h3 id="apiTasks" class="mb-0">0</h3>
                        <p class="card-text text-muted">API任务</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">
                            <i class="fas fa-paint-brush"></i>
                        </h5>
                        <h3 id="uiTasks" class="mb-0">0</h3>
                        <p class="card-text text-muted">UI任务</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning">
                            <i class="fas fa-bug"></i>
                        </h5>
                        <h3 id="testTasks" class="mb-0">0</h3>
                        <p class="card-text text-muted">测试任务</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务配置 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    任务配置
                </h5>
            </div>
            <div class="card-body">
                <form id="jiraConfigForm">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="jiraEnvironment" class="form-label">环境选择</label>
                                <select class="form-select" id="jiraEnvironment" required>
                                    <option value="">选择环境</option>
                                    <option value="test">测试环境</option>
                                    <option value="production">生产环境</option>
                                </select>
                                <div class="form-text">
                                    选择要创建任务的JIRA环境
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="jiraUsername" class="form-label">用户名/邮箱</label>
                                <input type="text" class="form-control" id="jiraUsername"
                                       placeholder="<EMAIL>" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="jiraToken" class="form-label">API Token</label>
                                <input type="password" class="form-control" id="jiraToken"
                                       placeholder="API Token" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="projectKey" class="form-label">项目Key</label>
                                <input type="text" class="form-control" id="projectKey"
                                       placeholder="PROJ" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sprint" class="form-label">迭代/Sprint</label>
                                <input type="text" class="form-control" id="sprint"
                                       placeholder="INST2025-sprint10" required>
                                <div class="form-text">
                                    所有任务将分配到此迭代
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="testAssignee" class="form-label">测试责任人</label>
                                <input type="text" class="form-control" id="testAssignee"
                                       placeholder="zhouqishu" value="zhouqishu">
                                <div class="form-text">
                                    测试责任人的OA账号，默认为zhouqishu
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    任务列表
                </h5>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="selectAll">
                        <i class="fas fa-check-square me-1"></i>
                        全选
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="deselectAll">
                        <i class="fas fa-square me-1"></i>
                        取消全选
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" id="expandAll">
                        <i class="fas fa-expand-alt me-1"></i>
                        展开全部
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" id="collapseAll">
                        <i class="fas fa-compress-alt me-1"></i>
                        折叠分组
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="tasksTable">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAllCheckbox" class="form-check-input">
                                </th>
                                <th>橙卡</th>
                                <th>模块</th>
                                <th>主任务</th>
                                <th>子任务</th>
                                <th>任务类型</th>
                                <th>负责人</th>
                                <th>工作量</th>
                            </tr>
                        </thead>
                        <tbody id="tasksTableBody">
                            <!-- 任务数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="d-flex justify-content-between mt-4">
            <a href="/" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                返回上传
            </a>
            <button type="button" class="btn btn-success btn-lg" id="createTasks" disabled>
                <i class="fas fa-plus-circle me-2"></i>
                创建选中的任务
                <span id="selectedCount" class="badge bg-light text-dark ms-2">0</span>
            </button>
        </div>

        <!-- 创建进度模态框 -->
        <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-cogs me-2"></i>
                            正在创建任务...
                        </h5>
                    </div>
                    <div class="modal-body">
                        <div class="progress mb-3">
                            <div id="createProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%">
                                <span id="createProgressText">0%</span>
                            </div>
                        </div>
                        <div id="createStatus" class="text-center">
                            准备创建任务...
                        </div>
                        <div id="createResults" class="mt-3" style="display: none;">
                            <!-- 创建结果将在这里显示 -->
                        </div>
                    </div>
                    <div class="modal-footer" id="modalFooter" style="display: none;">
                        <button type="button" class="btn btn-primary" onclick="window.location.href='/result'">
                            查看详细结果
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    let parsedTasks = [];
    let selectedTasks = [];
    
    // 从sessionStorage加载解析的任务数据
    function loadParsedTasks() {
        const tasksData = sessionStorage.getItem('parsedTasks');
        if (!tasksData) {
            // 如果没有数据，重定向到首页
            window.location.href = '/';
            return;
        }
        
        try {
            parsedTasks = JSON.parse(tasksData);
            displayTasks();
            updateStatistics();
        } catch (error) {
            console.error('解析任务数据失败:', error);
            alert('数据格式错误，请重新上传文件');
            window.location.href = '/';
        }
    }
    
    // 显示任务列表 - 支持层次结构展示
    function displayTasks() {
        const tbody = $('#tasksTableBody');
        tbody.empty();

        parsedTasks.forEach((task, index) => {
            // 根据层次级别添加不同的样式和缩进
            let rowClass = '';
            let indentStyle = '';
            let demandCell = task.demand || '';
            let moduleCell = task.module || '';
            let mainTaskCell = task.main_task || '';

            // 根据分组级别设置样式
            if (task.is_new_demand) {
                rowClass = 'task-hierarchy level-0 group-header';
            } else if (task.is_new_module) {
                rowClass = 'task-hierarchy level-1 group-header';
                demandCell = '<span class="merged-cell">↑</span>'; // 合并单元格指示
            } else if (task.is_new_main_task) {
                rowClass = 'task-hierarchy level-2 group-header';
                demandCell = '<span class="merged-cell">↑</span>'; // 合并单元格指示
                moduleCell = '<span class="merged-cell">↑</span>'; // 合并单元格指示
            } else {
                rowClass = 'task-hierarchy level-3';
                demandCell = '<span class="merged-cell">↑</span>'; // 合并单元格指示
                moduleCell = '<span class="merged-cell">↑</span>'; // 合并单元格指示
                mainTaskCell = '<span class="merged-cell">↑</span>'; // 合并单元格指示
            }

            // 为子任务添加简洁的缩进
            let subTaskDisplay = task.sub_task || '';
            if (task.group_level === 3 && !task.is_new_main_task) {
                subTaskDisplay = `<span class="sub-task-indent">${subTaskDisplay}</span>`;
            }

            // 移除任务类型图标，保持简洁

            const row = `
                <tr class="${rowClass}" data-group-level="${task.group_level}">
                    <td>
                        <input type="checkbox" class="form-check-input task-checkbox"
                               data-index="${index}" checked>
                    </td>
                    <td class="fw-bold text-primary">${demandCell}</td>
                    <td class="fw-bold text-info">${moduleCell}</td>
                    <td>${mainTaskCell}</td>
                    <td>${subTaskDisplay}</td>
                    <td>
                        <span class="badge ${getTaskTypeBadgeClass(task.task_type)}">
                            ${task.task_type || ''}
                        </span>
                    </td>
                    <td>${task.assignee || ''}</td>
                    <td>${task.workload || ''}d</td>
                </tr>
            `;
            tbody.append(row);
        });

        // 初始化选中状态
        updateSelectedTasks();
    }
    
    // 获取任务类型对应的badge样式
    function getTaskTypeBadgeClass(taskType) {
        switch (taskType) {
            case 'API': return 'bg-success';
            case 'UI': return 'bg-info';
            case '测试': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }
    
    // 更新统计信息
    function updateStatistics() {
        const total = parsedTasks.length;
        const apiCount = parsedTasks.filter(task => task.task_type === 'API').length;
        const uiCount = parsedTasks.filter(task => task.task_type === 'UI').length;
        const testCount = parsedTasks.filter(task => task.task_type === '测试').length;
        
        $('#totalTasks').text(total);
        $('#apiTasks').text(apiCount);
        $('#uiTasks').text(uiCount);
        $('#testTasks').text(testCount);
    }
    
    // 更新选中的任务
    function updateSelectedTasks() {
        selectedTasks = [];
        $('.task-checkbox:checked').each(function() {
            const index = $(this).data('index');
            selectedTasks.push(parsedTasks[index]);
        });
        
        $('#selectedCount').text(selectedTasks.length);
        $('#createTasks').prop('disabled', selectedTasks.length === 0);
        
        // 更新全选复选框状态
        const totalCheckboxes = $('.task-checkbox').length;
        const checkedCheckboxes = $('.task-checkbox:checked').length;
        
        if (checkedCheckboxes === 0) {
            $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#selectAllCheckbox').prop('indeterminate', true);
        }
    }
    
    // 事件处理
    $(document).on('change', '.task-checkbox', updateSelectedTasks);
    
    $('#selectAllCheckbox').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.task-checkbox').prop('checked', isChecked);
        updateSelectedTasks();
    });
    
    $('#selectAll').on('click', function() {
        $('.task-checkbox').prop('checked', true);
        updateSelectedTasks();
    });
    
    $('#deselectAll').on('click', function() {
        $('.task-checkbox').prop('checked', false);
        updateSelectedTasks();
    });

    // 展开全部
    $('#expandAll').on('click', function() {
        $('#tasksTableBody tr').show();
        $(this).hide();
        $('#collapseAll').show();
    });

    // 折叠分组 - 只显示分组标题行
    $('#collapseAll').on('click', function() {
        $('#tasksTableBody tr').each(function() {
            const $row = $(this);
            const groupLevel = parseInt($row.data('group-level'));

            // 只显示新分组的第一行（橙卡、模块、主任务的第一行）
            if ($row.hasClass('group-header')) {
                $row.show();
            } else {
                $row.hide();
            }
        });
        $(this).hide();
        $('#expandAll').show();
        updateSelectedTasks(); // 更新选中状态
    });
    


    // 创建任务
    $('#createTasks').on('click', function() {
        if (selectedTasks.length === 0) {
            alert('请选择要创建的任务');
            return;
        }

        const environment = $('#jiraEnvironment').val();
        if (!environment) {
            alert('请选择JIRA环境');
            return;
        }

        const config = {
            environment: environment,
            username: $('#jiraUsername').val(),
            token: $('#jiraToken').val(),
            project_key: $('#projectKey').val(),
            sprint: $('#sprint').val(),
            test_assignee: $('#testAssignee').val()
        };

        if (!config.environment || !config.username || !config.token || !config.project_key || !config.sprint) {
            alert('请填写完整的任务配置信息（测试责任人为可选项）');
            return;
        }

        // 显示进度模态框
        $('#progressModal').modal('show');

        // 模拟进度更新
        let progress = 0;
        const totalTasks = selectedTasks.length;
        const progressInterval = setInterval(function() {
            if (progress < 90) {
                progress += Math.random() * 10;
                if (progress > 90) progress = 90;
                $('#createProgressBar').css('width', progress + '%');
                $('#createProgressText').text(Math.round(progress) + '%');

                if (progress < 30) {
                    $('#createStatus').text('正在分析任务结构...');
                } else if (progress < 60) {
                    $('#createStatus').text('正在创建主任务...');
                } else if (progress < 90) {
                    $('#createStatus').text('正在创建子任务...');
                }
            }
        }, 500);

        // 发送创建请求
        $.ajax({
            url: '/api/v1/create-jira-tasks',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                tasks: selectedTasks,
                jira_config: config
            }),
            success: function(response) {
                clearInterval(progressInterval);

                if (response.success) {
                    // 存储创建结果
                    sessionStorage.setItem('createResults', JSON.stringify(response.data));

                    $('#createProgressBar').css('width', '100%');
                    $('#createProgressText').text('100%');
                    $('#createStatus').html('<span class="text-success"><i class="fas fa-check-circle me-2"></i>任务创建完成！</span>');
                    $('#modalFooter').show();
                } else {
                    $('#createProgressBar').css('width', '100%');
                    $('#createProgressText').text('100%');
                    $('#createStatus').html('<span class="text-danger"><i class="fas fa-times-circle me-2"></i>创建失败: ' + response.message + '</span>');
                }
            },
            error: function(xhr) {
                clearInterval(progressInterval);

                let errorMessage = '创建任务失败';
                if (xhr.responseJSON && xhr.responseJSON.detail) {
                    errorMessage = xhr.responseJSON.detail;
                }
                $('#createProgressBar').css('width', '100%');
                $('#createProgressText').text('100%');
                $('#createStatus').html('<span class="text-danger"><i class="fas fa-times-circle me-2"></i>' + errorMessage + '</span>');
            }
        });
    });

    // 页面加载时执行
    loadParsedTasks();
});
</script>
{% endblock %}
