{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-5">
            <h1 class="display-6">
                <i class="fas fa-question-circle text-primary me-2"></i>
                使用帮助
            </h1>
            <p class="lead text-muted">详细的使用说明和常见问题解答</p>
        </div>

        <!-- 快速开始 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    快速开始
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>第一步：准备Excel文件</h6>
                        <ul>
                            <li>下载Excel模板文件</li>
                            <li>按照模板格式填写任务信息</li>
                            <li>确保所有必填字段都已填写</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>第二步：上传和预览</h6>
                        <ul>
                            <li>在首页上传Excel文件</li>
                            <li>系统自动解析文件内容</li>
                            <li>在预览页面确认任务信息</li>
                        </ul>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>第三步：配置Jira连接</h6>
                        <ul>
                            <li>填写Jira服务器地址</li>
                            <li>输入用户名和API Token</li>
                            <li>选择目标项目和任务类型</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>第四步：批量创建任务</h6>
                        <ul>
                            <li>选择要创建的任务</li>
                            <li>点击创建按钮开始批量创建</li>
                            <li>查看创建结果和任务链接</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Excel文件格式要求 -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-excel me-2"></i>
                    Excel文件格式要求
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>列名</th>
                                <th>是否必填</th>
                                <th>说明</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>橙卡</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>需求的JIRA Key，用于关联父任务</td>
                                <td>JGKEZH-23876</td>
                            </tr>
                            <tr>
                                <td><strong>模块</strong></td>
                                <td><span class="badge bg-warning">可选</span></td>
                                <td>任务所属的功能模块</td>
                                <td>用户管理、订单处理</td>
                            </tr>
                            <tr>
                                <td><strong>主任务</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>主要任务的描述</td>
                                <td>实现用户登录功能</td>
                            </tr>
                            <tr>
                                <td><strong>子任务</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>具体的子任务描述</td>
                                <td>设计登录页面UI</td>
                            </tr>
                            <tr>
                                <td><strong>任务类型</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>任务的类型分类</td>
                                <td>UI、API、测试</td>
                            </tr>
                            <tr>
                                <td><strong>负责人</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>任务的负责人</td>
                                <td>张三、李四</td>
                            </tr>
                            <tr>
                                <td><strong>工作量</strong></td>
                                <td><span class="badge bg-warning">可选</span></td>
                                <td>预估的工作量（小时）</td>
                                <td>8、16、24</td>
                            </tr>
                            <tr>
                                <td><strong>迭代</strong></td>
                                <td><span class="badge bg-warning">可选</span></td>
                                <td>任务所属的迭代版本</td>
                                <td>Sprint 1、v1.0.0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Jira配置说明 -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Jira配置说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>服务器地址</h6>
                        <p>Jira服务器的完整URL地址，例如：</p>
                        <code>https://your-company.atlassian.net</code>
                        
                        <h6 class="mt-3">用户名/邮箱</h6>
                        <p>您的Jira账户邮箱地址，例如：</p>
                        <code><EMAIL></code>
                    </div>
                    <div class="col-md-6">
                        <h6>API Token</h6>
                        <p>Jira API访问令牌，获取步骤：</p>
                        <ol>
                            <li>登录Atlassian账户</li>
                            <li>进入"安全"设置</li>
                            <li>创建API Token</li>
                            <li>复制生成的Token</li>
                        </ol>
                        <a href="https://id.atlassian.com/manage-profile/security/api-tokens" 
                           target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt me-1"></i>
                            获取API Token
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常见问题 -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    常见问题
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse1">
                                Q: 上传Excel文件时提示格式错误怎么办？
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>请检查以下几点：</p>
                                <ul>
                                    <li>确保文件格式为.xlsx或.xls</li>
                                    <li>检查是否包含所有必填列</li>
                                    <li>确认列名与模板完全一致</li>
                                    <li>检查是否有空行或格式异常</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse2">
                                Q: Jira连接测试失败怎么办？
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>请检查以下配置：</p>
                                <ul>
                                    <li>确认Jira服务器地址正确</li>
                                    <li>检查用户名和API Token是否有效</li>
                                    <li>确认项目Key存在且有权限访问</li>
                                    <li>检查网络连接是否正常</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse3">
                                Q: 部分任务创建失败怎么办？
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>可以尝试以下解决方案：</p>
                                <ul>
                                    <li>查看失败原因的详细错误信息</li>
                                    <li>检查任务信息是否完整和正确</li>
                                    <li>确认负责人在Jira中存在</li>
                                    <li>使用重试功能重新创建失败的任务</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse4">
                                Q: 如何批量修改任务信息？
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>目前支持以下操作：</p>
                                <ul>
                                    <li>在预览页面可以选择性创建任务</li>
                                    <li>修改Excel文件后重新上传</li>
                                    <li>在Jira中手动修改已创建的任务</li>
                                    <li>使用导出功能保存创建结果</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 联系支持 -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-headset me-2"></i>
                    技术支持
                </h5>
            </div>
            <div class="card-body text-center">
                <p>如果您遇到其他问题或需要技术支持，请联系我们：</p>
                <div class="row">
                    <div class="col-md-4">
                        <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                        <p><strong>邮箱支持</strong><br><EMAIL></p>
                    </div>
                    <div class="col-md-4">
                        <i class="fas fa-phone fa-2x text-success mb-2"></i>
                        <p><strong>电话支持</strong><br>************</p>
                    </div>
                    <div class="col-md-4">
                        <i class="fas fa-comments fa-2x text-info mb-2"></i>
                        <p><strong>在线客服</strong><br>工作日 9:00-18:00</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
