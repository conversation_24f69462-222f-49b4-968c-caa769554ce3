{% extends "base.html" %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        姓名映射管理
                    </h5>
                    <div>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMappingModal">
                            <i class="fas fa-plus me-1"></i>
                            添加映射
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm ms-2" onclick="debugNameMapping()">
                            <i class="fas fa-bug me-1"></i>
                            调试
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted">
                                管理JIRA任务创建时的姓名到OA账号映射关系。当创建任务时，系统会自动将负责人的姓名转换为对应的OA账号。
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索姓名或OA账号..."
                                       oninput="console.log('内联搜索事件:', this.value); handleSearchInput.call(this, event);">
                                <button class="btn btn-outline-secondary" type="button" id="refreshBtn"
                                        onclick="console.log('内联刷新点击'); document.getElementById('searchInput').value=''; loadMappings();">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="loadingIndicator" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载映射数据...</p>
                    </div>

                    <div id="mappingTableContainer">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>姓名</th>
                                        <th>OA账号</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="mappingTableBody">
                                    <!-- 映射数据将在这里显示 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加映射模态框 -->
<div class="modal fade" id="addMappingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加姓名映射
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMappingForm">
                    <div class="mb-3">
                        <label for="nameInput" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="nameInput" required>
                        <div class="form-text">请输入完整的中文姓名</div>
                    </div>
                    <div class="mb-3">
                        <label for="loginInput" class="form-label">OA账号</label>
                        <input type="text" class="form-control" id="loginInput" required>
                        <div class="form-text">请输入对应的OA登录账号</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAddMapping">
                    <i class="fas fa-save me-1"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
var allMappingData = [];
var filteredMappingData = [];

// 全局调试函数
window.debugNameMapping = function() {
    console.log('=== 姓名映射调试信息 ===');
    console.log('searchInput:', document.getElementById('searchInput'));
    console.log('refreshBtn:', document.getElementById('refreshBtn'));
    console.log('confirmAddMapping:', document.getElementById('confirmAddMapping'));
    console.log('nameInput:', document.getElementById('nameInput'));
    console.log('loginInput:', document.getElementById('loginInput'));
    console.log('allMappingData:', allMappingData);
    console.log('filteredMappingData:', filteredMappingData);

    // 测试搜索功能
    console.log('测试搜索功能...');
    try {
        searchMappings('test');
    } catch (error) {
        console.error('搜索测试失败:', error);
    }

    // 测试添加功能
    console.log('测试添加功能...');
    var nameInput = document.getElementById('nameInput');
    var loginInput = document.getElementById('loginInput');
    if (nameInput && loginInput) {
        nameInput.value = '测试用户' + Date.now();
        loginInput.value = 'testuser' + Date.now();
        try {
            addMapping();
        } catch (error) {
            console.error('添加测试失败:', error);
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('文档加载完成');

    // 添加调试信息
    console.log('DOM元素检查:');
    console.log('searchInput:', document.getElementById('searchInput'));
    console.log('refreshBtn:', document.getElementById('refreshBtn'));
    console.log('confirmAddMapping:', document.getElementById('confirmAddMapping'));
    console.log('nameInput:', document.getElementById('nameInput'));
    console.log('loginInput:', document.getElementById('loginInput'));

    // 立即初始化，不延迟
    console.log('立即初始化...');
    initEventListeners();
    loadMappings();
});

// 备用初始化 - 确保在页面完全加载后再次尝试
window.addEventListener('load', function() {
    console.log('页面完全加载完成，进行备用初始化检查...');

    // 检查事件是否已经绑定
    var searchInput = document.getElementById('searchInput');
    var refreshBtn = document.getElementById('refreshBtn');
    var confirmAddBtn = document.getElementById('confirmAddMapping');

    console.log('备用检查 - searchInput:', searchInput);
    console.log('备用检查 - refreshBtn:', refreshBtn);
    console.log('备用检查 - confirmAddBtn:', confirmAddBtn);

    // 如果元素存在但事件可能没有绑定，重新绑定
    if (searchInput || refreshBtn || confirmAddBtn) {
        console.log('重新初始化事件监听器...');
        initEventListeners();
    }
});

// 初始化事件监听器
function initEventListeners() {
    console.log('初始化事件监听器...');

    // 搜索功能
    var searchInput = document.getElementById('searchInput');
    console.log('搜索输入框元素:', searchInput);
    if (searchInput) {
        console.log('添加搜索事件监听器...');

        // 移除可能存在的旧事件监听器
        searchInput.removeEventListener('input', handleSearchInput);

        // 添加新的事件监听器
        searchInput.addEventListener('input', handleSearchInput);

        // 也添加 keyup 事件作为备用
        searchInput.removeEventListener('keyup', handleSearchKeyup);
        searchInput.addEventListener('keyup', handleSearchKeyup);

        console.log('搜索事件监听器已添加');
    } else {
        console.error('找不到搜索输入框元素');
    }

    // 刷新按钮
    var refreshBtn = document.getElementById('refreshBtn');
    console.log('刷新按钮元素:', refreshBtn);
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            console.log('点击刷新按钮');
            // 清空搜索框
            var searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
            }
            // 重新加载全部数据
            loadMappings();
        });
        console.log('刷新事件监听器已添加');
    } else {
        console.error('找不到刷新按钮元素');
    }

    // 添加映射按钮
    var confirmAddBtn = document.getElementById('confirmAddMapping');
    console.log('确认添加按钮元素:', confirmAddBtn);
    if (confirmAddBtn) {
        console.log('添加点击事件监听器...');

        // 移除可能存在的旧事件监听器
        confirmAddBtn.removeEventListener('click', handleAddMappingClick);

        // 添加新的事件监听器
        confirmAddBtn.addEventListener('click', handleAddMappingClick);

        console.log('添加映射事件监听器已添加');
    } else {
        console.error('找不到确认添加按钮元素');
    }
}

// 搜索输入事件处理函数
function handleSearchInput(event) {
    console.log('搜索输入事件触发:', event);
    var searchTerm = this.value.trim();
    console.log('搜索输入变化:', searchTerm);

    // 直接调用搜索
    try {
        searchMappings(searchTerm);
    } catch (error) {
        console.error('搜索调用失败:', error);
    }
}

// 搜索按键事件处理函数
function handleSearchKeyup(event) {
    console.log('搜索按键事件触发:', event);
    var searchTerm = this.value.trim();
    console.log('搜索按键变化:', searchTerm);

    // 直接调用搜索
    try {
        searchMappings(searchTerm);
    } catch (error) {
        console.error('搜索调用失败:', error);
    }
}

// 添加映射按钮点击事件处理函数
function handleAddMappingClick(event) {
    console.log('点击确认添加按钮，事件:', event);
    event.preventDefault(); // 防止默认行为
    event.stopPropagation(); // 阻止事件冒泡

    try {
        addMapping();
    } catch (error) {
        console.error('添加映射调用失败:', error);
    }
}

// 加载映射数据
function loadMappings() {
    console.log('开始加载映射数据...');

    // 显示加载状态
    var loadingIndicator = document.getElementById('loadingIndicator');
    var tableContainer = document.getElementById('mappingTableContainer');

    if (loadingIndicator) loadingIndicator.classList.remove('d-none');
    if (tableContainer) tableContainer.classList.add('d-none');

    fetch('/api/v1/jira/name-mapping')
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            console.log('API调用成功:', data);
            if (data.success && data.data) {
                allMappingData = data.data;
                filteredMappingData = allMappingData.slice(); // 复制数组
                displayMappings(filteredMappingData);
                showAlert('success', '成功加载 ' + data.count + ' 条映射记录');
            } else {
                showAlert('danger', '加载映射数据失败');
            }
        })
        .catch(function(error) {
            console.error('API调用失败:', error);
            showAlert('danger', '加载映射数据失败：' + error.message);
        })
        .finally(function() {
            if (loadingIndicator) loadingIndicator.classList.add('d-none');
            if (tableContainer) tableContainer.classList.remove('d-none');
        });
}

// 显示映射数据
function displayMappings(data) {
    console.log('显示映射数据:', data);
    var tbody = document.getElementById('mappingTableBody');

    if (!tbody) {
        console.error('找不到表格body元素');
        return;
    }

    tbody.innerHTML = '';

    if (data.length === 0) {
        var emptyRow = document.createElement('tr');
        emptyRow.innerHTML =
            '<td colspan="4" class="text-center text-muted">' +
                '<i class="fas fa-inbox me-2"></i>' +
                '暂无映射数据' +
            '</td>';
        tbody.appendChild(emptyRow);
        return;
    }

    data.forEach(function(mapping) {
        var row = document.createElement('tr');
        row.innerHTML =
            '<td>' + mapping.id + '</td>' +
            '<td><strong>' + mapping.name + '</strong></td>' +
            '<td><code>' + mapping.login + '</code></td>' +
            '<td>' +
                '<button class="btn btn-sm btn-outline-danger" onclick="deleteMapping(' + mapping.id + ', \'' + mapping.name + '\')">' +
                    '<i class="fas fa-trash"></i>' +
                '</button>' +
            '</td>';
        tbody.appendChild(row);
    });
}

// 搜索映射数据（调用后端API）
function searchMappings(searchTerm) {
    console.log('开始搜索映射数据，搜索词:', searchTerm);

    try {
        // 显示加载状态
        var loadingIndicator = document.getElementById('loadingIndicator');
        var tableContainer = document.getElementById('mappingTableContainer');

        if (loadingIndicator) loadingIndicator.classList.remove('d-none');
        if (tableContainer) tableContainer.classList.add('d-none');

        // 构建搜索URL
        var searchUrl = '/api/v1/jira/name-mapping/search';
        if (searchTerm) {
            searchUrl += '?q=' + encodeURIComponent(searchTerm);
        }

        console.log('调用搜索API:', searchUrl);

        fetch(searchUrl)
            .then(function(response) {
                console.log('搜索API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }
                return response.json();
            })
            .then(function(data) {
                console.log('搜索API响应数据:', data);
                if (data.success && data.data) {
                    filteredMappingData = data.data;
                    displayMappings(filteredMappingData);

                    // 显示搜索结果提示
                    var message = searchTerm ?
                        '搜索 "' + searchTerm + '" 找到 ' + data.count + ' 条记录' :
                        '显示全部 ' + data.count + ' 条记录';
                    showAlert('info', message);
                } else {
                    console.error('搜索API返回失败:', data);
                    showAlert('danger', '搜索失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(function(error) {
                console.error('搜索API调用失败:', error);
                showAlert('danger', '搜索失败：' + error.message);
            })
            .finally(function() {
                if (loadingIndicator) loadingIndicator.classList.add('d-none');
                if (tableContainer) tableContainer.classList.remove('d-none');
            });
    } catch (error) {
        console.error('搜索函数执行失败:', error);
        showAlert('danger', '搜索功能异常：' + error.message);
    }
}

// 保留原来的过滤函数作为备用（本地过滤）
function filterMappingsLocal(searchTerm) {
    console.log('本地过滤映射数据，搜索词:', searchTerm);

    if (!searchTerm) {
        filteredMappingData = allMappingData.slice();
    } else {
        filteredMappingData = allMappingData.filter(function(mapping) {
            var nameMatch = mapping.name.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1;
            var loginMatch = mapping.login.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1;
            return nameMatch || loginMatch;
        });
    }
    displayMappings(filteredMappingData);
}

// 添加映射
function addMapping() {
    console.log('开始添加映射...');

    try {
        var nameInput = document.getElementById('nameInput');
        var loginInput = document.getElementById('loginInput');

        if (!nameInput || !loginInput) {
            console.error('找不到输入框元素');
            showAlert('danger', '找不到输入框元素');
            return;
        }

        var name = nameInput.value.trim();
        var login = loginInput.value.trim();

        console.log('输入的数据:', { name: name, login: login });

        if (!name || !login) {
            showAlert('warning', '请填写完整的姓名和OA账号');
            return;
        }

        var confirmBtn = document.getElementById('confirmAddMapping');
        if (!confirmBtn) {
            console.error('找不到确认按钮');
            return;
        }

        var originalText = confirmBtn.innerHTML;

        // 显示加载状态
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
        confirmBtn.disabled = true;

        console.log('发送API请求...');

    fetch('/api/v1/jira/name-mapping', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: name,
            login: login
        })
    })
    .then(function(response) {
        console.log('API响应状态:', response.status);
        return response.json();
    })
    .then(function(data) {
        console.log('API响应数据:', data);
        if (data.success) {
            // 关闭模态框 - 使用更兼容的方式
            var modal = document.getElementById('addMappingModal');
            if (modal) {
                // 尝试使用Bootstrap 5的方式
                try {
                    var bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    } else {
                        // 如果没有实例，创建一个新的
                        var newModal = new bootstrap.Modal(modal);
                        newModal.hide();
                    }
                } catch (e) {
                    console.error('关闭模态框失败:', e);
                    // 手动关闭模态框
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');
                    var backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                }
            }

            // 清空表单
            nameInput.value = '';
            loginInput.value = '';

            // 重新加载数据
            loadMappings();
            showAlert('success', data.message);
        } else {
            showAlert('danger', '添加映射失败：' + (data.message || '未知错误'));
        }
    })
    .catch(function(error) {
        console.error('添加映射失败:', error);
        showAlert('danger', '添加映射失败：' + error.message);
    })
    .finally(function() {
        // 恢复按钮状态
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });

    } catch (error) {
        console.error('添加映射函数执行失败:', error);
        showAlert('danger', '添加映射功能异常：' + error.message);

        // 恢复按钮状态
        var confirmBtn = document.getElementById('confirmAddMapping');
        if (confirmBtn) {
            confirmBtn.innerHTML = '<i class="fas fa-save me-1"></i>保存';
            confirmBtn.disabled = false;
        }
    }
}

// 删除映射
function deleteMapping(id, name) {
    if (!confirm('确定要删除姓名 "' + name + '" 的映射吗？')) {
        return;
    }

    fetch('/api/v1/jira/name-mapping/' + id, {
        method: 'DELETE'
    })
    .then(function(response) {
        return response.json();
    })
    .then(function(data) {
        if (data.success) {
            showAlert('success', data.message);
            loadMappings(); // 重新加载数据
        } else {
            showAlert('danger', '删除映射失败：' + (data.message || '未知错误'));
        }
    })
    .catch(function(error) {
        console.error('删除映射失败:', error);
        showAlert('danger', '删除映射失败：' + error.message);
    });
}

// 显示提示信息
function showAlert(type, message) {
    // 移除现有的提示
    var existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(function(alert) {
        alert.remove();
    });

    // 创建新的提示
    var alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-' + type + ' alert-dismissible fade show';
    alertDiv.innerHTML = message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';

    // 插入到页面顶部
    var container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // 3秒后自动消失
    setTimeout(function() {
        if (alertDiv.parentNode) {
            alertDiv.classList.remove('show');
            setTimeout(function() {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 150);
        }
    }, 3000);
}
</script>
{% endblock %}
