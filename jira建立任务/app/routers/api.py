#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: api.py
描述: API路由

更新日志:
2024-01-15 20:33 - 创建了API路由模块
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
import pandas as pd
import json
import logging
from datetime import datetime
import io
from app.database import db_manager
from config.jira import jira_config
from app.services.jira_service import jira_service

# 创建路由实例
router = APIRouter()

@router.post("/v1/parse-excel")
async def parse_excel(
    file: UploadFile = File(...),
    sheet_name: Optional[str] = Form(None)
):
    """
    解析Excel文件，返回任务列表供预览
    
    参数:
    - file: Excel文件
    - sheet_name: 工作表名称（可选）
    
    返回:
    - 解析后的任务列表
    """
    try:
        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=400, 
                detail="文件格式不支持，请上传Excel文件（.xlsx或.xls）"
            )
        
        # 读取Excel文件
        content = await file.read()

        # 解析Excel内容
        try:
            # 使用BytesIO包装字节内容
            excel_buffer = io.BytesIO(content)
            if sheet_name:
                df = pd.read_excel(excel_buffer, sheet_name=sheet_name)
            else:
                df = pd.read_excel(excel_buffer)
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件解析失败: {str(e)}"
            )
        
        # 打印实际的列名用于调试
        actual_columns = list(df.columns)
        logging.info(f"Excel文件实际列名: {actual_columns}")

        # 验证必要的列是否存在 - 使用更灵活的匹配（移除迭代字段）
        required_columns = ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量']

        # 创建列名映射，支持去除空格和不同的列名变体
        column_mapping = {}
        for req_col in required_columns:
            found = False
            for actual_col in actual_columns:
                # 去除空格并比较
                if str(actual_col).strip() == req_col:
                    column_mapping[req_col] = actual_col
                    found = True
                    break
            if not found:
                # 尝试一些常见的变体
                if req_col == '橙卡':
                    for actual_col in actual_columns:
                        if '橙卡' in str(actual_col) or 'demand' in str(actual_col).lower():
                            column_mapping[req_col] = actual_col
                            found = True
                            break
                elif req_col == '任务类型':
                    for actual_col in actual_columns:
                        if '任务类型' in str(actual_col) or '类型' in str(actual_col):
                            column_mapping[req_col] = actual_col
                            found = True
                            break
                # 可以添加更多变体匹配

        missing_columns = [col for col in required_columns if col not in column_mapping]

        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件缺少必要的列: {', '.join(missing_columns)}。实际列名: {', '.join(actual_columns)}"
            )

        logging.info(f"列名映射: {column_mapping}")
        
        # 处理合并单元格 - 向前填充空值
        # 对于可能合并的列进行前向填充
        merge_columns = ['橙卡', '模块', '主任务']
        for col_name in merge_columns:
            if col_name in column_mapping:
                actual_col = column_mapping[col_name]
                df[actual_col] = df[actual_col].ffill()  # 使用新的方法

        logging.info(f"处理合并单元格后的数据预览:\n{df.head()}")

        # 转换数据格式 - 使用英文字段名作为API标准格式
        tasks = []
        current_demand = ""
        current_module = ""
        current_main_task = ""

        for index, row in df.iterrows():
            # 获取当前行的值
            demand = str(row[column_mapping['橙卡']]) if pd.notna(row[column_mapping['橙卡']]) else ""
            module = str(row[column_mapping['模块']]) if pd.notna(row[column_mapping['模块']]) else ""
            main_task = str(row[column_mapping['主任务']]) if pd.notna(row[column_mapping['主任务']]) else ""
            sub_task = str(row[column_mapping['子任务']]) if pd.notna(row[column_mapping['子任务']]) else ""

            # 判断是否是新的分组
            is_new_demand = demand != current_demand and demand != ""
            is_new_module = module != current_module and module != ""
            is_new_main_task = main_task != current_main_task and main_task != ""

            # 更新当前分组状态
            if is_new_demand:
                current_demand = demand
            if is_new_module:
                current_module = module
            if is_new_main_task:
                current_main_task = main_task

            task = {
                "row": index + 2,  # Excel行号从2开始（第1行是标题）
                "demand": demand,
                "module": module,
                "main_task": main_task,
                "sub_task": sub_task,
                "task_type": str(row[column_mapping['任务类型']]) if pd.notna(row[column_mapping['任务类型']]) else "",
                "assignee": str(row[column_mapping['负责人']]) if pd.notna(row[column_mapping['负责人']]) else "",
                "workload": str(row[column_mapping['工作量']]) if pd.notna(row[column_mapping['工作量']]) else "",
                # 添加分组标识
                "is_new_demand": is_new_demand,
                "is_new_module": is_new_module,
                "is_new_main_task": is_new_main_task,
                "group_level": 3 if sub_task else (2 if main_task else (1 if module else 0))
            }
            tasks.append(task)
        
        return {
            "success": True,
            "message": f"成功解析{len(tasks)}个任务",
            "data": {
                "filename": file.filename,
                "total_count": len(tasks),
                "tasks": tasks,
                "parsed_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.post("/v1/create-issues")
async def create_issues(
    tasks: List[Dict[str, Any]],
    jira_config: Dict[str, str]
):
    """
    创建Jira任务
    
    参数:
    - tasks: 任务列表
    - jira_config: Jira配置信息
    
    返回:
    - 创建结果
    """
    try:
        # 验证Jira配置
        required_config = ['server', 'username', 'password', 'project_key']
        missing_config = [key for key in required_config if key not in jira_config]
        
        if missing_config:
            raise HTTPException(
                status_code=400,
                detail=f"Jira配置缺少必要参数: {', '.join(missing_config)}"
            )
        
        # TODO: 实现Jira任务创建逻辑
        # 这里先返回模拟结果
        results = []
        for task in tasks:
            result = {
                "row_number": task.get("row_number"),
                "main_task": task.get("main_task"),
                "sub_task": task.get("sub_task"),
                "status": "创建成功",
                "jira_key": f"TEST-{task.get('row_number', 0)}",
                "message": "模拟创建成功"
            }
            results.append(result)
        
        return {
            "success": True,
            "message": f"成功创建{len(results)}个任务",
            "data": {
                "total_count": len(results),
                "success_count": len(results),
                "failed_count": 0,
                "results": results,
                "created_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/v1/task-types")
async def get_task_types():
    """获取任务类型列表"""
    task_types = [
        {"id": "11007", "name": "迭代任务", "description": "主任务类型"},
        {"id": "11012", "name": "前端开发子任务", "description": "UI相关的子任务"},
        {"id": "11013", "name": "后端开发子任务", "description": "API相关的子任务"},
        {"id": "11014", "name": "数据开发子任务", "description": "测试相关的子任务"}
    ]
    
    return {
        "success": True,
        "data": task_types
    }

@router.get("/v1/users")
async def get_users():
    """获取用户列表"""
    # TODO: 从数据库获取用户信息
    users = [
        {"oa_account": "zhangsan", "chinese_name": "张三", "email": "<EMAIL>"},
        {"oa_account": "lisi", "chinese_name": "李四", "email": "<EMAIL>"},
        {"oa_account": "wangwu", "chinese_name": "王五", "email": "<EMAIL>"}
    ]
    
    return {
        "success": True,
        "data": users
    }

@router.post("/v1/test-jira-connection")
async def test_jira_connection(jira_config: Dict[str, str]):
    """测试Jira连接"""
    try:
        # 验证必要的配置参数
        required_fields = ['url', 'username', 'token', 'project_key']
        for field in required_fields:
            if field not in jira_config or not jira_config[field]:
                raise HTTPException(
                    status_code=400,
                    detail=f"缺少必要的配置参数: {field}"
                )

        # TODO: 实际的Jira连接测试逻辑
        # 这里应该使用atlassian-python-api库来测试连接
        # from atlassian import Jira
        # jira = Jira(
        #     url=jira_config['url'],
        #     username=jira_config['username'],
        #     password=jira_config['token']
        # )
        # project = jira.project(jira_config['project_key'])

        # 暂时返回成功，实际实现时需要真正测试连接
        return {
            "success": True,
            "message": "连接测试成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}"
        }

@router.post("/v1/create-jira-tasks")
async def create_jira_tasks(request_data: Dict[str, Any]):
    """批量创建Jira任务"""
    try:
        tasks = request_data.get('tasks', [])
        jira_config = request_data.get('jira_config', {})

        if not tasks:
            raise HTTPException(
                status_code=400,
                detail="没有要创建的任务"
            )

        # 验证Jira配置
        required_fields = ['environment', 'username', 'token', 'project_key', 'sprint']
        for field in required_fields:
            if field not in jira_config or not jira_config[field]:
                raise HTTPException(
                    status_code=400,
                    detail=f"缺少必要的任务配置: {field}"
                )

        # 获取环境URL
        from config.jira import jira_config as jira_env_config
        environment = jira_config['environment']
        jira_url = jira_env_config.get_environment_url(environment)

        if not jira_url:
            raise HTTPException(
                status_code=400,
                detail=f"无效的环境配置: {environment}"
            )

        # 使用JIRA服务创建任务
        logging.info(f"开始创建 {len(tasks)} 个JIRA任务，环境: {jira_config['environment']}")

        result = jira_service.batch_create_issues(jira_config, tasks)

        success_tasks = result.get('success', [])
        failed_tasks = result.get('failed', [])

        return {
            "success": True,
            "data": {
                "success": success_tasks,
                "failed": failed_tasks,
                "summary": {
                    "total": len(tasks),
                    "success_count": len(success_tasks),
                    "failed_count": len(failed_tasks)
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )

@router.post("/v1/export-results")
async def export_results(request_data: Dict[str, Any]):
    """导出创建结果"""
    try:
        results = request_data.get('results', {})
        include_success = request_data.get('include_success', True)
        include_failed = request_data.get('include_failed', True)
        include_summary = request_data.get('include_summary', True)

        # TODO: 实际的Excel导出逻辑
        # 这里应该使用pandas和openpyxl来生成Excel文件

        # 模拟导出结果
        filename = f"jira_tasks_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        download_url = f"/api/v1/download/{filename}"

        return {
            "success": True,
            "filename": filename,
            "download_url": download_url,
            "message": "导出成功"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"导出失败: {str(e)}"
        )

@router.get("/v1/database/test")
async def test_database_connection():
    """测试数据库连接"""
    try:
        is_connected = db_manager.test_connection()
        if is_connected:
            return {
                "success": True,
                "message": "数据库连接成功",
                "database": "srms",
                "host": "************:15020"
            }
        else:
            return {
                "success": False,
                "message": "数据库连接失败"
            }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"数据库连接测试失败: {str(e)}"
        )

@router.get("/v1/database/tables")
async def get_database_tables():
    """获取数据库表列表"""
    try:
        query = """
        SELECT
            TABLE_NAME as table_name,
            TABLE_COMMENT as table_comment,
            TABLE_ROWS as table_rows
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = :schema
        ORDER BY TABLE_NAME
        """
        tables = db_manager.execute_query(query, {'schema': 'srms'})
        return {
            "success": True,
            "data": tables,
            "count": len(tables)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取数据库表列表失败: {str(e)}"
        )

@router.get("/v1/database/table/{table_name}")
async def get_table_info(table_name: str):
    """获取表结构信息"""
    try:
        table_info = db_manager.get_table_info(table_name)
        return {
            "success": True,
            "table_name": table_name,
            "columns": table_info,
            "column_count": len(table_info)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取表结构信息失败: {str(e)}"
        )

@router.get("/v1/jira/environments")
async def get_jira_environments():
    """获取JIRA环境配置"""
    try:
        from config.jira import jira_config
        environments = jira_config.get_all_environments()
        return {
            "success": True,
            "data": environments
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取JIRA环境配置失败: {str(e)}"
        )

@router.get("/v1/jira/name-mapping")
async def get_name_mappings():
    """获取姓名到OA账号的映射列表"""
    try:
        query = "SELECT id, name, login FROM t_jira_name_map ORDER BY name"
        mappings = db_manager.execute_query(query)
        return {
            "success": True,
            "data": mappings,
            "count": len(mappings)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取姓名映射失败: {str(e)}"
        )

@router.post("/v1/jira/name-mapping")
async def add_name_mapping(request_data: Dict[str, Any]):
    """添加姓名到OA账号的映射"""
    try:
        name = request_data.get('name', '').strip()
        login = request_data.get('login', '').strip()

        if not name or not login:
            raise HTTPException(
                status_code=400,
                detail="姓名和OA账号不能为空"
            )

        # 检查是否已存在
        check_query = "SELECT id FROM t_jira_name_map WHERE name = :name"
        existing = db_manager.execute_query(check_query, {'name': name})

        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"姓名 '{name}' 的映射已存在"
            )

        # 插入新映射
        insert_query = "INSERT INTO t_jira_name_map (name, login) VALUES (:name, :login)"
        db_manager.execute_update(insert_query, {'name': name, 'login': login})

        return {
            "success": True,
            "message": f"成功添加姓名映射: {name} -> {login}"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"添加姓名映射失败: {str(e)}"
        )

@router.get("/v1/jira/name-mapping/search")
async def search_name_mappings(q: str = ""):
    """搜索姓名到OA账号的映射"""
    try:
        if not q or not q.strip():
            # 如果搜索词为空，返回所有映射
            query = "SELECT id, name, login FROM t_jira_name_map ORDER BY name"
            mappings = db_manager.execute_query(query)
        else:
            # 模糊搜索姓名和OA账号
            search_term = f"%{q.strip()}%"
            query = """
                SELECT id, name, login FROM t_jira_name_map
                WHERE name LIKE :search_term OR login LIKE :search_term
                ORDER BY name
            """
            mappings = db_manager.execute_query(query, {'search_term': search_term})

        return {
            "success": True,
            "data": mappings,
            "count": len(mappings),
            "search_term": q.strip()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"搜索姓名映射失败: {str(e)}"
        )

@router.get("/v1/jira/name-mapping/{name}")
async def get_oa_login(name: str):
    """根据姓名获取OA账号"""
    try:
        query = "SELECT login FROM t_jira_name_map WHERE name = :name"
        result = db_manager.execute_query(query, {'name': name})

        if result and len(result) > 0:
            return {
                "success": True,
                "name": name,
                "login": result[0]['login']
            }
        else:
            return {
                "success": False,
                "message": f"未找到姓名 '{name}' 对应的OA账号"
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询OA账号失败: {str(e)}"
        )

@router.delete("/v1/jira/name-mapping/{mapping_id}")
async def delete_name_mapping(mapping_id: int):
    """删除姓名到OA账号的映射"""
    try:
        # 检查映射是否存在
        check_query = "SELECT name FROM t_jira_name_map WHERE id = :id"
        existing = db_manager.execute_query(check_query, {'id': mapping_id})

        if not existing:
            raise HTTPException(
                status_code=404,
                detail=f"ID为 {mapping_id} 的映射不存在"
            )

        name = existing[0]['name']

        # 删除映射
        delete_query = "DELETE FROM t_jira_name_map WHERE id = :id"
        db_manager.execute_update(delete_query, {'id': mapping_id})

        return {
            "success": True,
            "message": f"成功删除姓名映射: {name}"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除姓名映射失败: {str(e)}"
        )