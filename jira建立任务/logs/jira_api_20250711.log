2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:324] - 任务数量: 1
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:328] - Sprint: INST2025-Sprint10
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 1 个主任务, 1 个子任务
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:334] - 分析结果: 1 个主任务, 1 个子任务
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:447] - 需要创建 1 个主任务
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task请求详情:
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 08:42:26 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建main_task响应详情:
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:42:43 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '524x532294x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=66E0B337653E30E6A78610CC0EBEF979; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_07a104bffe58a6233878161aeab8842ce2fb92a4_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1ug08wt', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857049",
  "key": "JGKEZH-12428",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857049"
}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:604] - 成功创建main_task: JGKEZH-12428
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:475] - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12428
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:493] - 主任务创建完成: 成功 1 个，失败 0 个
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:633] - ============================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:634] - 步骤2: 开始Sprint关联
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:635] - Sprint名称: INST2025-Sprint10
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:636] - 需要关联 1 个主任务
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:637] - ============================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:708] - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=INST2025-Sprint10
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:713] - Sprint查询请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Accept': 'application/json'}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:717] - Sprint查询响应状态码: 200
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:718] - Sprint查询响应: {"suggestions":[],"allMatches":[]}
2025-07-11 08:42:43 - jira_api - ERROR - [jira_service.py:736] - 未找到Sprint: INST2025-Sprint10
2025-07-11 08:42:43 - jira_api - ERROR - [jira_service.py:668] - 未找到Sprint: INST2025-Sprint10
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:692] - Sprint关联完成: 成功 0 个
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:798] - ============================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:799] - 步骤3: 开始需求关联
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:800] - ============================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:812] - 关联需求 JGKEZH-24332 到主任务 JGKEZH-12428
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:868] - 关联需求 JGKEZH-24332 到任务 JGKEZH-12428
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:869] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12428
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:874] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:875] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-24332"
          }
        }
      }
    ]
  }
}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:884] - 需求关联响应状态码: 400
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:885] - 需求关联响应: {"errorMessages":["问题不存在"],"errors":{}}
2025-07-11 08:42:43 - jira_api - ERROR - [jira_service.py:891] - 需求 JGKEZH-24332 关联到任务 JGKEZH-12428 异常: 需求关联失败: HTTP 400 - {"errorMessages":["问题不存在"],"errors":{}}
2025-07-11 08:42:43 - jira_api - ERROR - [jira_service.py:819] - 需求 JGKEZH-24332 关联失败: 需求关联失败: HTTP 400 - {"errorMessages":["问题不存在"],"errors":{}}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:832] - 需求关联完成: 成功 0 个
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:900] - ============================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:901] - 步骤4: 开始创建子任务
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:902] - 需要创建 1 个子任务
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:903] - 成功创建的主任务数量: 1
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:904] - ============================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:941] - 有效子任务: 1 个
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:942] - 因主任务失败而跳过的子任务: 0 个
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:951] - 创建子任务 1/1: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12428
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d\n任务类型: UI",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12428"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "2h",
      "remainingEstimate": "2h"
    }
  }
}
2025-07-11 08:42:43 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:42:45 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '524x532297x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=AB9C38737DEF0B12B98ED65D29643A96; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_12c921b337c32d6f1a16a7e4be16fbb40fafa9a1_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1t76i2e', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857050",
  "key": "JGKEZH-12429",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857050"
}
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12429
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12429
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:996] - 子任务创建完成: 成功 1 个，失败 0 个
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:374] - 总任务数: 1
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 1 个
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 1 个
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 0 个
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:378] - 需求关联: 0 个
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:379] - 成功: 2 个
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:380] - 失败: 2 个
2025-07-11 08:42:45 - jira_api - INFO - [jira_service.py:381] - ================================================================================
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:324] - 任务数量: 11
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:328] - Sprint: BSP2022-sprint9
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS代办通知邮件
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:415] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS端「产品目录树」
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:334] - 分析结果: 5 个主任务, 11 个子任务
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:447] - 需要创建 5 个主任务
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task请求详情:
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 08:46:47 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建main_task响应详情:
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:48 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532851x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=2C74B262314D3E65639937F291756B45; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_52bd2f5f699511b60e6edeeea2b1617b3ab73851_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1chyno4', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857051",
  "key": "JGKEZH-12430",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857051"
}
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:604] - 成功创建main_task: JGKEZH-12430
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:475] - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12430
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS代办通知邮件
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task请求详情:
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 08:46:48 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建main_task响应详情:
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:49 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532852x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=D12D8C20546545D266685B0C1934C6CA; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_e7378809fe5056832a58e312a0e8ffdb660d99d9_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1yufnx1', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857052",
  "key": "JGKEZH-12431",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857052"
}
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:604] - 成功创建main_task: JGKEZH-12431
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:475] - 主任务创建成功: CMS代办通知邮件 -> JGKEZH-12431
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:452] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task请求详情:
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 08:46:49 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建main_task响应详情:
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:50 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532853x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=32FB303562FA5257940E8B0721BB2E73; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_58ae316361d6097510319e0562e1d85eb74f06a0_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '6c8o4p', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857053",
  "key": "JGKEZH-12432",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857053"
}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:604] - 成功创建main_task: JGKEZH-12432
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:475] - 主任务创建成功: PB系统支持退回删除操作员申请 -> JGKEZH-12432
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS端「产品目录树」
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task请求详情:
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建main_task响应详情:
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:50 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532854x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=35950AE029E08BCE246606A9878C9ACF; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_8e42f4c981214beb7fc4b78b0b6c9e178bca55f3_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1y29d8a', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857054",
  "key": "JGKEZH-12433",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857054"
}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:604] - 成功创建main_task: JGKEZH-12433
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:475] - 主任务创建成功: CMS端「产品目录树」 -> JGKEZH-12433
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task请求详情:
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 08:46:50 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建main_task响应详情:
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:51 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532855x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=153674CAE4F5620BE37FE5A3FBCDF6CF; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_5696f449e952a0e483155abf3dd3990567937b2e_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'mqblqs', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857055",
  "key": "JGKEZH-12434",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857055"
}
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:604] - 成功创建main_task: JGKEZH-12434
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:475] - 主任务创建成功: CMS「产品管理-开通列表」优化 -> JGKEZH-12434
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:493] - 主任务创建完成: 成功 5 个，失败 0 个
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:633] - ============================================================
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:634] - 步骤2: 开始Sprint关联
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:635] - Sprint名称: BSP2022-sprint9
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:636] - 需要关联 5 个主任务
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:637] - ============================================================
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:708] - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=BSP2022-sprint9
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:713] - Sprint查询请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Accept': 'application/json'}
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:717] - Sprint查询响应状态码: 200
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:718] - Sprint查询响应: {"suggestions":[{"name":"BSP2022-sprint9","id":6514,"stateKey":"ACTIVE","boardName":"机构客户APP-Scrum","date":"2022-05-05T09:12:27Z"}],"allMatches":[]}
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:726] - 从suggestions找到Sprint ID: 6514
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:644] - 找到Sprint ID: 6514
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:767] - 关联任务 JGKEZH-12430 到Sprint 6514
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:768] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12430
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:773] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:774] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:783] - Sprint关联响应状态码: 204
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:784] - Sprint关联响应: 
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:651] - 任务 JGKEZH-12430 成功关联到Sprint BSP2022-sprint9
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:767] - 关联任务 JGKEZH-12431 到Sprint 6514
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:768] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12431
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:773] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:51 - jira_api - INFO - [jira_service.py:774] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:783] - Sprint关联响应状态码: 204
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:784] - Sprint关联响应: 
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:651] - 任务 JGKEZH-12431 成功关联到Sprint BSP2022-sprint9
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:767] - 关联任务 JGKEZH-12432 到Sprint 6514
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:768] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12432
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:773] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:774] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:783] - Sprint关联响应状态码: 204
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:784] - Sprint关联响应: 
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:651] - 任务 JGKEZH-12432 成功关联到Sprint BSP2022-sprint9
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:767] - 关联任务 JGKEZH-12433 到Sprint 6514
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:768] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12433
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:773] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:774] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:783] - Sprint关联响应状态码: 204
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:784] - Sprint关联响应: 
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:651] - 任务 JGKEZH-12433 成功关联到Sprint BSP2022-sprint9
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:767] - 关联任务 JGKEZH-12434 到Sprint 6514
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:768] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12434
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:773] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:52 - jira_api - INFO - [jira_service.py:774] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:783] - Sprint关联响应状态码: 204
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:784] - Sprint关联响应: 
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:651] - 任务 JGKEZH-12434 成功关联到Sprint BSP2022-sprint9
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:692] - Sprint关联完成: 成功 5 个
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:798] - ============================================================
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:799] - 步骤3: 开始需求关联
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:800] - ============================================================
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:812] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12430
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:868] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12430
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:869] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12430
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:874] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:875] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:884] - 需求关联响应状态码: 204
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:885] - 需求关联响应: 
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:815] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12430
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:812] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12431
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:868] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12431
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:869] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12431
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:874] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:875] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:884] - 需求关联响应状态码: 204
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:885] - 需求关联响应: 
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:815] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12431
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:812] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12432
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:868] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12432
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:869] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12432
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:874] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:53 - jira_api - INFO - [jira_service.py:875] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:884] - 需求关联响应状态码: 204
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:885] - 需求关联响应: 
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:815] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12432
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:812] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12433
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:868] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12433
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:869] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12433
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:874] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:875] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:884] - 需求关联响应状态码: 204
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:885] - 需求关联响应: 
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:815] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12433
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:812] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12434
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:868] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12434
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:869] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12434
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:874] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:875] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:884] - 需求关联响应状态码: 204
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:885] - 需求关联响应: 
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:815] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12434
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:832] - 需求关联完成: 成功 5 个
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:900] - ============================================================
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:901] - 步骤4: 开始创建子任务
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:902] - 需要创建 11 个子任务
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:903] - 成功创建的主任务数量: 5
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:904] - ============================================================
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:941] - 有效子任务: 11 个
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:942] - 因主任务失败而跳过的子任务: 0 个
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:951] - 创建子任务 1/11: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12430
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "description": "橙卡: JGKEZH-11433\n模块: 三季度优化\n预估工作量: 0.3d\n任务类型: UI",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12430"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "2h",
      "remainingEstimate": "2h"
    }
  }
}
2025-07-11 08:46:54 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:55 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532867x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=6988F5E439367A2226CB225E6E3C9574; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_11734fec375f747a2c465fb4260606d72317b0a2_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '18fflxe', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857056",
  "key": "JGKEZH-12435",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857056"
}
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12435
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12435
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:951] - 创建子任务 2/11: 发送邮件模板通知调整 -> 主任务: JGKEZH-12431
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "description": "橙卡: JGKEZH-11433\n模块: 三季度优化\n预估工作量: 0.3d\n任务类型: 后端",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12431"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "2h",
      "remainingEstimate": "2h"
    }
  }
}
2025-07-11 08:46:55 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:56 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532868x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=6C23B10650E24BE302AE3E3DDF324003; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d4f14a3c499404337163ff137660dfd0b7a5f5ab_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1avqs3j', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857057",
  "key": "JGKEZH-12436",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857057"
}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12436
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 发送邮件模板通知调整 -> JGKEZH-12436
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:951] - 创建子任务 3/11: cms审核支持退回 -> 主任务: JGKEZH-12432
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "cms审核支持退回",
    "description": "橙卡: JGKEZH-11433\n模块: 三季度优化\n预估工作量: 0.1d\n任务类型: UI",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12432"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0h",
      "remainingEstimate": "0h"
    }
  }
}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:56 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532869x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=288515C45983D7D78F3F6289521CE651; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_a9aef266dd2a86aeff4992a4510f80ad9f64e0b8_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'jo3pua', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857058",
  "key": "JGKEZH-12437",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857058"
}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12437
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: cms审核支持退回 -> JGKEZH-12437
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:951] - 创建子任务 4/11: web删除操作员逻辑调整 -> 主任务: JGKEZH-12432
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "web删除操作员逻辑调整",
    "description": "橙卡: JGKEZH-11433\n模块: 三季度优化\n预估工作量: 1.0d\n任务类型: API",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12432"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "8h",
      "remainingEstimate": "8h"
    }
  }
}
2025-07-11 08:46:56 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:57 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532870x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=4660B8CD857C03B29607A5537215C265; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_e136fa92930098f76b14fd2bd9824b805e30ef9e_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'xoy9ry', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857059",
  "key": "JGKEZH-12438",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857059"
}
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12438
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: web删除操作员逻辑调整 -> JGKEZH-12438
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:951] - 创建子任务 5/11: CMS删除操作员逻辑调整 -> 主任务: JGKEZH-12432
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS删除操作员逻辑调整",
    "description": "橙卡: JGKEZH-11433\n模块: 三季度优化\n预估工作量: 1.0d\n任务类型: 测试",
    "issuetype": {
      "id": "11014"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12432"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "8h",
      "remainingEstimate": "8h"
    }
  }
}
2025-07-11 08:46:57 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:58 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532871x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=DCD2ABC7756634684E4B42319D550A8A; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_b8a929ffabe0b46e91c4f10d39561808bc087a75_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'o1dom4', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857060",
  "key": "JGKEZH-12439",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857060"
}
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12439
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: CMS删除操作员逻辑调整 -> JGKEZH-12439
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:951] - 创建子任务 6/11: 产品目录树列表和筛选 -> 主任务: JGKEZH-12433
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表和筛选",
    "description": "橙卡: JGKEZH-11521\n模块: 三季度优化\n预估工作量: 1.0d\n任务类型: UI",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12433"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "8h",
      "remainingEstimate": "8h"
    }
  }
}
2025-07-11 08:46:58 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:46:59 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532872x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=8805635A459804477C9F0A36BCD13A50; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_a18da192fef01447f8e1a6ae01ff6b020f3783f5_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '11gcjyr', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857061",
  "key": "JGKEZH-12440",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857061"
}
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12440
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 产品目录树列表和筛选 -> JGKEZH-12440
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:951] - 创建子任务 7/11: 产品管理新增【关联产品目录树】选项 -> 主任务: JGKEZH-12433
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品管理新增【关联产品目录树】选项",
    "description": "橙卡: JGKEZH-11521\n模块: 三季度优化\n预估工作量: 0.5d\n任务类型: UI",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12433"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "4h",
      "remainingEstimate": "4h"
    }
  }
}
2025-07-11 08:46:59 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:47:00 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532873x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=7108B26EC21A5229968E8DA4F4A3C84A; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_50778942ca510f3eb1e601ab40e13f80444e6630_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'v0hx5k', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857062",
  "key": "JGKEZH-12441",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857062"
}
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12441
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 产品管理新增【关联产品目录树】选项 -> JGKEZH-12441
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:951] - 创建子任务 8/11: 产品目录树列表 -> 主任务: JGKEZH-12433
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表",
    "description": "橙卡: JGKEZH-11521\n模块: 三季度优化\n预估工作量: 0.5d\n任务类型: API",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12433"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "4h",
      "remainingEstimate": "4h"
    }
  }
}
2025-07-11 08:47:00 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:47:01 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532874x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=26F60E31F52D8F10B1040AC5488477BB; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_6171a1bb736a3ca06a92e45b193855ecc433f0ad_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1pwrdgh', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857063",
  "key": "JGKEZH-12442",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857063"
}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12442
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 产品目录树列表 -> JGKEZH-12442
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:951] - 创建子任务 9/11: 目录新增&编辑 -> 主任务: JGKEZH-12433
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "目录新增&编辑",
    "description": "橙卡: JGKEZH-11521\n模块: 三季度优化\n预估工作量: 0.5d\n任务类型: API",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12433"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "4h",
      "remainingEstimate": "4h"
    }
  }
}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:47:01 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532875x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=F6683B2C018F411BC92BE68AFFD990D3; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d290a77f9b3f6c89804c2cdf8af52278d379602c_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '9yk9ss', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857064",
  "key": "JGKEZH-12443",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857064"
}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12443
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 目录新增&编辑 -> JGKEZH-12443
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:951] - 创建子任务 10/11: 筛选项新增 类型 -> 主任务: JGKEZH-12434
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "筛选项新增 类型",
    "description": "橙卡: JGKEZH-11521\n模块: 三季度优化\n预估工作量: 0.2d\n任务类型: UI",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12434"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "1h",
      "remainingEstimate": "1h"
    }
  }
}
2025-07-11 08:47:01 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:47:02 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532876x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=3C1CA961AAA3A242C3C81D8EF3008635; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_530dc9d8e2664f7180774b0c203ccff81a4c9853_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '4i1i51', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857065",
  "key": "JGKEZH-12444",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857065"
}
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12444
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 筛选项新增 类型 -> JGKEZH-12444
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:951] - 创建子任务 11/11: 开通列表支持类型筛选 -> 主任务: JGKEZH-12434
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:568] - ================================================================================
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建sub_task请求详情:
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:570] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:571] - 请求方法: POST
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:572] - 认证用户: lidezheng
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:577] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:578] - 请求体 (JSON):
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:579] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "开通列表支持类型筛选",
    "description": "橙卡: JGKEZH-11521\n模块: 三季度优化\n预估工作量: 0.3d\n任务类型: API",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12434"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "2h",
      "remainingEstimate": "2h"
    }
  }
}
2025-07-11 08:47:02 - jira_api - INFO - [jira_service.py:580] - ================================================================================
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:590] - JIRA API 创建sub_task响应详情:
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:591] - 响应状态码: 201
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:592] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 00:47:03 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '528x532877x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=130DE3341E55023BA5B78AF667CE1346; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_bd1d39886d4b7fa7ad83a54de3ae2ead9aaa64dc_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1yyq1is', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:593] - 响应体:
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:596] - {
  "id": "857066",
  "key": "JGKEZH-12445",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857066"
}
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:599] - ================================================================================
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:604] - 成功创建sub_task: JGKEZH-12445
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:975] - 子任务创建成功: 开通列表支持类型筛选 -> JGKEZH-12445
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:996] - 子任务创建完成: 成功 11 个，失败 0 个
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:374] - 总任务数: 11
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 5 个
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 11 个
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 5 个
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:378] - 需求关联: 5 个
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:379] - 成功: 16 个
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:380] - 失败: 0 个
2025-07-11 08:47:03 - jira_api - INFO - [jira_service.py:381] - ================================================================================
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:324] - 任务数量: 9
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:328] - Sprint: INST2025-sprint10
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:415] - 发现主任务: MCP优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: MCP数据分析 -> 主任务: MCP优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:415] - 发现主任务: 用户权限优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 2 个主任务, 9 个子任务
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:334] - 分析结果: 2 个主任务, 9 个子任务
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:447] - 需要创建 2 个主任务
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 10:06:56 - jira_api - INFO - [jira_service.py:452] - 创建主任务: MCP优化
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:570] - ================================================================================
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:571] - JIRA API 创建main_task请求详情:
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:572] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:573] - 请求方法: POST
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:574] - 认证用户: lidezheng
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:579] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:580] - 请求体 (JSON):
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:581] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "MCP优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:582] - ================================================================================
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:592] - JIRA API 创建main_task响应详情:
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:593] - 响应状态码: 502
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:594] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:595] - 响应体:
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:600] - 响应文本: 
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:601] - ================================================================================
2025-07-11 10:06:57 - jira_api - ERROR - [jira_service.py:619] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:06:57 - jira_api - ERROR - [jira_service.py:623] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:06:57 - jira_api - ERROR - [jira_service.py:482] - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:452] - 创建主任务: 用户权限优化
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:570] - ================================================================================
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:571] - JIRA API 创建main_task请求详情:
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:572] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:573] - 请求方法: POST
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:574] - 认证用户: lidezheng
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:579] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:580] - 请求体 (JSON):
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:581] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "用户权限优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:06:57 - jira_api - INFO - [jira_service.py:582] - ================================================================================
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:592] - JIRA API 创建main_task响应详情:
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:593] - 响应状态码: 502
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:594] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:595] - 响应体:
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:600] - 响应文本: 
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:601] - ================================================================================
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:619] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:623] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:482] - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:495] - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:800] - ============================================================
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:801] - 步骤3: 开始需求关联
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:802] - ============================================================
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:834] - 需求关联完成: 成功 0 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:902] - ============================================================
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:903] - 步骤4: 开始创建子任务
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:904] - 需要创建 9 个子任务
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:905] - 成功创建的主任务数量: 0
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:906] - ============================================================
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 'MCP优化' 创建失败，子任务 'web端 应用文案全部替换为fundx应用' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 'MCP优化' 创建失败，子任务 'cms端 应用文案全部替换为fundx应用' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 'MCP优化' 创建失败，子任务 'MCP优化测试' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 'MCP优化' 创建失败，子任务 'MCP数据分析' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 'MCP优化' 创建失败，子任务 'CMS自动生成【MCP介绍】' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 'MCP优化' 创建失败，子任务 'mcp服务可用工具列表接口' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 '用户权限优化' 创建失败，子任务 '用户角色管理界面优化' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 '用户权限优化' 创建失败，子任务 '权限验证API接口' 无法创建
2025-07-11 10:06:58 - jira_api - ERROR - [jira_service.py:927] - 主任务 '用户权限优化' 创建失败，子任务 '权限功能测试' 无法创建
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:944] - 有效子任务: 0 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:945] - 因主任务失败而跳过的子任务: 9 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:999] - 子任务创建完成: 成功 0 个，失败 9 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:374] - 总任务数: 9
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 0 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 0 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 0 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:378] - 需求关联: 0 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:379] - 成功: 0 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:380] - 失败: 11 个
2025-07-11 10:06:58 - jira_api - INFO - [jira_service.py:381] - ================================================================================
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:324] - 任务数量: 11
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:328] - Sprint: BSP2022-sprint9
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS代办通知邮件
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:415] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS端「产品目录树」
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:334] - 分析结果: 5 个主任务, 11 个子任务
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:447] - 需要创建 5 个主任务
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:570] - ================================================================================
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:571] - JIRA API 创建main_task请求详情:
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:572] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:573] - 请求方法: POST
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:574] - 认证用户: lidezheng
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:579] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:580] - 请求体 (JSON):
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:581] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:33:07 - jira_api - INFO - [jira_service.py:582] - ================================================================================
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:592] - JIRA API 创建main_task响应详情:
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:593] - 响应状态码: 502
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:594] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:595] - 响应体:
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:600] - 响应文本: 
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:601] - ================================================================================
2025-07-11 10:33:08 - jira_api - ERROR - [jira_service.py:619] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:33:08 - jira_api - ERROR - [jira_service.py:623] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:33:08 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS代办通知邮件
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:570] - ================================================================================
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:571] - JIRA API 创建main_task请求详情:
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:572] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:573] - 请求方法: POST
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:574] - 认证用户: lidezheng
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:579] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:580] - 请求体 (JSON):
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:581] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:582] - ================================================================================
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:592] - JIRA API 创建main_task响应详情:
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:593] - 响应状态码: 502
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:594] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:595] - 响应体:
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:600] - 响应文本: 
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:601] - ================================================================================
2025-07-11 10:33:08 - jira_api - ERROR - [jira_service.py:619] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:33:08 - jira_api - ERROR - [jira_service.py:623] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:33:08 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:452] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:570] - ================================================================================
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:571] - JIRA API 创建main_task请求详情:
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:572] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:573] - 请求方法: POST
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:574] - 认证用户: lidezheng
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:579] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:580] - 请求体 (JSON):
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:581] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:33:08 - jira_api - INFO - [jira_service.py:582] - ================================================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:592] - JIRA API 创建main_task响应详情:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:593] - 响应状态码: 502
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:594] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:595] - 响应体:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:600] - 响应文本: 
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:601] - ================================================================================
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:619] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:623] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:482] - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS端「产品目录树」
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:570] - ================================================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:571] - JIRA API 创建main_task请求详情:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:572] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:573] - 请求方法: POST
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:574] - 认证用户: lidezheng
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:579] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:580] - 请求体 (JSON):
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:581] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:582] - ================================================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:592] - JIRA API 创建main_task响应详情:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:593] - 响应状态码: 502
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:594] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:595] - 响应体:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:600] - 响应文本: 
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:601] - ================================================================================
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:619] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:623] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS端「产品目录树」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:570] - ================================================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:571] - JIRA API 创建main_task请求详情:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:572] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:573] - 请求方法: POST
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:574] - 认证用户: lidezheng
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:579] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:580] - 请求体 (JSON):
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:581] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:582] - ================================================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:592] - JIRA API 创建main_task响应详情:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:593] - 响应状态码: 502
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:594] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:595] - 响应体:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:600] - 响应文本: 
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:601] - ================================================================================
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:619] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:623] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS「产品管理-开通列表」优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:495] - 主任务创建完成: 成功 0 个，失败 5 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:800] - ============================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:801] - 步骤3: 开始需求关联
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:802] - ============================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:834] - 需求关联完成: 成功 0 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:902] - ============================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:903] - 步骤4: 开始创建子任务
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:904] - 需要创建 11 个子任务
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:905] - 成功创建的主任务数量: 0
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:906] - ============================================================
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS代办通知邮件' 创建失败，子任务 '发送邮件模板通知调整' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'cms审核支持退回' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'web删除操作员逻辑调整' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'CMS删除操作员逻辑调整' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表和筛选' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品管理新增【关联产品目录树】选项' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '目录新增&编辑' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '筛选项新增 类型' 无法创建
2025-07-11 10:33:09 - jira_api - ERROR - [jira_service.py:927] - 主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '开通列表支持类型筛选' 无法创建
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:944] - 有效子任务: 0 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:945] - 因主任务失败而跳过的子任务: 11 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:999] - 子任务创建完成: 成功 0 个，失败 11 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:374] - 总任务数: 11
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 0 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 0 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 0 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:378] - 需求关联: 0 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:379] - 成功: 0 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:380] - 失败: 16 个
2025-07-11 10:33:09 - jira_api - INFO - [jira_service.py:381] - ================================================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:324] - 任务数量: 11
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:328] - Sprint: BSP2022-sprint9
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS代办通知邮件
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:415] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS端「产品目录树」
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:334] - 分析结果: 5 个主任务, 11 个子任务
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:447] - 需要创建 5 个主任务
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:570] - JIRA API 创建main_task请求详情:
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:571] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:572] - 请求方法: POST
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:573] - 认证用户: lidezheng
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:578] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:579] - 请求体 (JSON):
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:580] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:581] - ================================================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:591] - JIRA API 创建main_task响应详情:
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:592] - 响应状态码: 502
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:593] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:594] - 响应体:
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:599] - 响应文本: 
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:600] - ================================================================================
2025-07-11 10:35:59 - jira_api - ERROR - [jira_service.py:618] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:35:59 - jira_api - ERROR - [jira_service.py:622] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:35:59 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS代办通知邮件
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:570] - JIRA API 创建main_task请求详情:
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:571] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:572] - 请求方法: POST
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:573] - 认证用户: lidezheng
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:578] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:579] - 请求体 (JSON):
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:580] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:35:59 - jira_api - INFO - [jira_service.py:581] - ================================================================================
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:591] - JIRA API 创建main_task响应详情:
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:592] - 响应状态码: 502
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:593] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:594] - 响应体:
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:599] - 响应文本: 
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:600] - ================================================================================
2025-07-11 10:36:00 - jira_api - ERROR - [jira_service.py:618] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:36:00 - jira_api - ERROR - [jira_service.py:622] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:36:00 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:452] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:570] - JIRA API 创建main_task请求详情:
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:571] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:572] - 请求方法: POST
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:573] - 认证用户: lidezheng
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:578] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:579] - 请求体 (JSON):
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:580] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:581] - ================================================================================
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:591] - JIRA API 创建main_task响应详情:
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:592] - 响应状态码: 502
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:593] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:594] - 响应体:
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:599] - 响应文本: 
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:600] - ================================================================================
2025-07-11 10:36:00 - jira_api - ERROR - [jira_service.py:618] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:36:00 - jira_api - ERROR - [jira_service.py:622] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:36:00 - jira_api - ERROR - [jira_service.py:482] - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS端「产品目录树」
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:570] - JIRA API 创建main_task请求详情:
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:571] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:572] - 请求方法: POST
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:573] - 认证用户: lidezheng
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:578] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:579] - 请求体 (JSON):
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:580] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:36:00 - jira_api - INFO - [jira_service.py:581] - ================================================================================
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:591] - JIRA API 创建main_task响应详情:
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:592] - 响应状态码: 502
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:593] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:594] - 响应体:
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:599] - 响应文本: 
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:600] - ================================================================================
2025-07-11 10:36:05 - jira_api - ERROR - [jira_service.py:618] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:36:05 - jira_api - ERROR - [jira_service.py:622] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:36:05 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS端「产品目录树」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:570] - JIRA API 创建main_task请求详情:
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:571] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:572] - 请求方法: POST
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:573] - 认证用户: lidezheng
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:578] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:579] - 请求体 (JSON):
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:580] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 10:36:05 - jira_api - INFO - [jira_service.py:581] - ================================================================================
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:591] - JIRA API 创建main_task响应详情:
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:592] - 响应状态码: 502
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:593] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:594] - 响应体:
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:599] - 响应文本: 
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:600] - ================================================================================
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:618] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:622] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:482] - 主任务 'CMS「产品管理-开通列表」优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:495] - 主任务创建完成: 成功 0 个，失败 5 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:799] - ============================================================
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:800] - 步骤3: 开始需求关联
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:801] - ============================================================
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:833] - 需求关联完成: 成功 0 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:901] - ============================================================
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:902] - 步骤4: 开始创建子任务
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:903] - 需要创建 11 个子任务
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:904] - 成功创建的主任务数量: 0
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:905] - ============================================================
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS代办通知邮件' 创建失败，子任务 '发送邮件模板通知调整' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'cms审核支持退回' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'web删除操作员逻辑调整' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'CMS删除操作员逻辑调整' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表和筛选' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品管理新增【关联产品目录树】选项' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '目录新增&编辑' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '筛选项新增 类型' 无法创建
2025-07-11 10:36:06 - jira_api - ERROR - [jira_service.py:926] - 主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '开通列表支持类型筛选' 无法创建
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:943] - 有效子任务: 0 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:944] - 因主任务失败而跳过的子任务: 11 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:998] - 子任务创建完成: 成功 0 个，失败 11 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:374] - 总任务数: 11
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 0 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 0 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 0 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:378] - 需求关联: 0 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:379] - 成功: 0 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:380] - 失败: 16 个
2025-07-11 10:36:06 - jira_api - INFO - [jira_service.py:381] - ================================================================================
