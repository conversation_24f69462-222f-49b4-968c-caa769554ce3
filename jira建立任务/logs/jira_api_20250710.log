2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:241] - ================================================================================
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:242] - JIRA 连接测试详情:
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:243] - 测试URL: http://jirauat.gf.com.cn/rest/api/2/myself
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:244] - 请求方法: GET
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:245] - 认证用户: test_user
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:246] - 环境: test
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:247] - ================================================================================
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:256] - JIRA 连接测试响应:
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:257] - 响应状态码: 502
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:258] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:259] - 响应体:
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:264] - 响应文本: 
2025-07-10 17:32:48 - jira_api - INFO - [jira_service.py:265] - ================================================================================
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:117] - ================================================================================
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:118] - JIRA API 请求详情:
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:119] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:120] - 请求方法: POST
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:121] - 认证用户: lidezheng
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:122] - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:123] - 请求体 (JSON):
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:124] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: CMS图片裁剪功能优化\n预估工作量: 0.3d\n任务类型: UI"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    },
    "assignee": {
      "name": "guanyuan"
    }
  }
}
2025-07-10 17:38:05 - jira_api - INFO - [jira_service.py:125] - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:135] - JIRA API 响应详情:
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:136] - 响应状态码: 502
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:137] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:138] - 响应体:
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:143] - 响应文本: 
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:144] - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:117] - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:118] - JIRA API 请求详情:
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:119] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:120] - 请求方法: POST
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:121] - 认证用户: lidezheng
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:122] - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:123] - 请求体 (JSON):
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:124] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: CMS代办通知邮件\n预估工作量: 0.3d\n任务类型: 后端"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    }
  }
}
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:125] - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:135] - JIRA API 响应详情:
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:136] - 响应状态码: 502
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:137] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:138] - 响应体:
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:143] - 响应文本: 
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:144] - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:117] - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:118] - JIRA API 请求详情:
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:119] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:120] - 请求方法: POST
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:121] - 认证用户: lidezheng
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:122] - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:123] - 请求体 (JSON):
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:124] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "提示文案更新",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: ATP专业系统申请优化\n预估工作量: 0.1d\n任务类型: UI"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    },
    "assignee": {
      "name": "guanyuan"
    }
  }
}
2025-07-10 17:38:06 - jira_api - INFO - [jira_service.py:125] - ================================================================================
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:135] - JIRA API 响应详情:
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:136] - 响应状态码: 502
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:137] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:138] - 响应体:
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:143] - 响应文本: 
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:144] - ================================================================================
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:117] - ================================================================================
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:118] - JIRA API 请求详情:
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:119] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:120] - 请求方法: POST
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:121] - 认证用户: lidezheng
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:122] - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:123] - 请求体 (JSON):
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:124] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "需要开通的极速柜台，改为必填",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: ATP专业系统申请优化\n预估工作量: 0.2d\n任务类型: UI"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    },
    "assignee": {
      "name": "guanyuan"
    }
  }
}
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:125] - ================================================================================
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:135] - JIRA API 响应详情:
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:136] - 响应状态码: 502
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:137] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:138] - 响应体:
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:143] - 响应文本: 
2025-07-10 17:38:07 - jira_api - INFO - [jira_service.py:144] - ================================================================================
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:307] - ================================================================================
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:308] - 开始按层次结构批量创建JIRA任务
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:309] - 任务数量: 8
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:310] - 环境: test
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:311] - 项目: TEST
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:312] - 用户: test_user
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:313] - Sprint: INST2025-sprint10
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:314] - ================================================================================
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:381] - 开始分析任务层次结构...
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:400] - 发现主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:400] - 发现主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:418] - 任务结构分析完成: 2 个主任务, 8 个子任务
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:319] - 分析结果: 2 个主任务, 8 个子任务
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:430] - ============================================================
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:431] - 步骤1: 开始创建主任务
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:432] - 需要创建 2 个主任务
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:433] - ============================================================
2025-07-10 17:54:53 - jira_api - INFO - [jira_service.py:437] - 创建主任务: MCP优化
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:564] - ================================================================================
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:565] - JIRA API 创建main_task请求详情:
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:566] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:567] - 请求方法: POST
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:568] - 认证用户: test_user
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:569] - 请求体 (JSON):
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:570] - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "MCP优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-23876\n模块: 《MCP追加第四、五大点》\n预估工作量: 0.3d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:54:54 - jira_api - INFO - [jira_service.py:571] - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:581] - JIRA API 创建main_task响应详情:
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:582] - 响应状态码: 502
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:583] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:584] - 响应体:
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:589] - 响应文本: 
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:590] - ================================================================================
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:608] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:612] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:466] - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:437] - 创建主任务: 用户权限优化
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:564] - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:565] - JIRA API 创建main_task请求详情:
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:566] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:567] - 请求方法: POST
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:568] - 认证用户: test_user
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:569] - 请求体 (JSON):
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:570] - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "用户权限优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24150\n模块: 用户管理模块\n预估工作量: 2d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:571] - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:581] - JIRA API 创建main_task响应详情:
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:582] - 响应状态码: 502
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:583] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:584] - 响应体:
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:589] - 响应文本: 
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:590] - ================================================================================
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:608] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:612] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:466] - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:478] - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:772] - ============================================================
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:773] - 步骤3: 开始需求关联
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:774] - ============================================================
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:806] - 需求关联完成: 成功 0 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:864] - ============================================================
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:865] - 步骤4: 开始创建子任务
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:866] - 需要创建 8 个子任务
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:867] - ============================================================
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 'web端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 'cms端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 'MCP优化测试' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 'CMS自动生成【MCP介绍】' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 'mcp服务可用工具列表接口' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 '用户角色管理界面优化' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 '权限验证API接口' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - [jira_service.py:884] - 子任务 '权限功能测试' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:942] - 子任务创建完成: 成功 0 个，失败 8 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:357] - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:358] - JIRA 批量任务创建完成:
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:359] - 总任务数: 8
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:360] - 主任务创建: 0 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:361] - 子任务创建: 0 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:362] - Sprint关联: 0 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:363] - 需求关联: 0 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:364] - 成功: 0 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:365] - 失败: 10 个
2025-07-10 17:54:55 - jira_api - INFO - [jira_service.py:366] - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:307] - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:308] - 开始按层次结构批量创建JIRA任务
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:309] - 任务数量: 10
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:310] - 环境: test
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:311] - 项目: JGKEZH
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:312] - 用户: lidezheng
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:313] - Sprint: INST2025-Sprint10
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:314] - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:381] - 开始分析任务层次结构...
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:400] - 发现主任务: CMS图片裁剪功能优化
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:400] - 发现主任务: CMS代办通知邮件
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:400] - 发现主任务: ATP专业系统申请优化
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 提示文案更新 -> 主任务: ATP专业系统申请优化
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 需要开通的极速柜台，改为必填 -> 主任务: ATP专业系统申请优化
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:400] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: web端退回申请的展示 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: cms端退回申请的展示 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:400] - 发现主任务: Web端「个人中心」
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 申请订单页-【申请期限】字段反显sku的配置值 -> 主任务: Web端「个人中心」
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:418] - 任务结构分析完成: 5 个主任务, 10 个子任务
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:319] - 分析结果: 5 个主任务, 10 个子任务
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:430] - ============================================================
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:431] - 步骤1: 开始创建主任务
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:432] - 需要创建 5 个主任务
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:433] - ============================================================
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:437] - 创建主任务: CMS图片裁剪功能优化
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:564] - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:565] - JIRA API 创建main_task请求详情:
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:566] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:567] - 请求方法: POST
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:568] - 认证用户: lidezheng
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:569] - 请求体 (JSON):
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:570] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:57:58 - jira_api - INFO - [jira_service.py:571] - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:581] - JIRA API 创建main_task响应详情:
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:582] - 响应状态码: 502
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:583] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:584] - 响应体:
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:589] - 响应文本: 
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:590] - ================================================================================
2025-07-10 17:57:59 - jira_api - ERROR - [jira_service.py:608] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - [jira_service.py:612] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - [jira_service.py:466] - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:437] - 创建主任务: CMS代办通知邮件
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:564] - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:565] - JIRA API 创建main_task请求详情:
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:566] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:567] - 请求方法: POST
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:568] - 认证用户: lidezheng
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:569] - 请求体 (JSON):
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:570] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "wxhuangyichen"
    },
    "customfield_13103": {
      "name": "wxhuangyichen"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:571] - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:581] - JIRA API 创建main_task响应详情:
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:582] - 响应状态码: 502
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:583] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:584] - 响应体:
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:589] - 响应文本: 
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:590] - ================================================================================
2025-07-10 17:57:59 - jira_api - ERROR - [jira_service.py:608] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - [jira_service.py:612] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - [jira_service.py:466] - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:437] - 创建主任务: ATP专业系统申请优化
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:564] - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:565] - JIRA API 创建main_task请求详情:
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:566] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:567] - 请求方法: POST
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:568] - 认证用户: lidezheng
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:569] - 请求体 (JSON):
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:570] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "ATP专业系统申请优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:57:59 - jira_api - INFO - [jira_service.py:571] - ================================================================================
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:581] - JIRA API 创建main_task响应详情:
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:582] - 响应状态码: 502
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:583] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:584] - 响应体:
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:589] - 响应文本: 
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:590] - ================================================================================
2025-07-10 17:58:00 - jira_api - ERROR - [jira_service.py:608] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:58:00 - jira_api - ERROR - [jira_service.py:612] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:58:00 - jira_api - ERROR - [jira_service.py:466] - 主任务 'ATP专业系统申请优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:437] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:564] - ================================================================================
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:565] - JIRA API 创建main_task请求详情:
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:566] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:567] - 请求方法: POST
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:568] - 认证用户: lidezheng
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:569] - 请求体 (JSON):
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:570] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:58:00 - jira_api - INFO - [jira_service.py:571] - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:581] - JIRA API 创建main_task响应详情:
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:582] - 响应状态码: 502
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:583] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:584] - 响应体:
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:589] - 响应文本: 
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:590] - ================================================================================
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:608] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:612] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:466] - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:437] - 创建主任务: Web端「个人中心」
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:564] - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:565] - JIRA API 创建main_task请求详情:
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:566] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:567] - 请求方法: POST
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:568] - 认证用户: lidezheng
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:569] - 请求体 (JSON):
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:570] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "Web端「个人中心」",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-23514\n模块: 开放广发专区优化\n预估工作量: 0.2d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:571] - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:581] - JIRA API 创建main_task响应详情:
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:582] - 响应状态码: 502
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:583] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:584] - 响应体:
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:589] - 响应文本: 
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:590] - ================================================================================
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:608] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:612] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:466] - 主任务 'Web端「个人中心」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:478] - 主任务创建完成: 成功 0 个，失败 5 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:772] - ============================================================
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:773] - 步骤3: 开始需求关联
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:774] - ============================================================
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:806] - 需求关联完成: 成功 0 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:864] - ============================================================
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:865] - 步骤4: 开始创建子任务
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:866] - 需要创建 10 个子任务
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:867] - ============================================================
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 '多个场景设定图片上传比例' 的主任务 'CMS图片裁剪功能优化' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 '发送邮件模板通知调整' 的主任务 'CMS代办通知邮件' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 '提示文案更新' 的主任务 'ATP专业系统申请优化' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 '需要开通的极速柜台，改为必填' 的主任务 'ATP专业系统申请优化' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 'cms审核支持退回' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 'web端退回申请的展示' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 'cms端退回申请的展示' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 'web删除操作员逻辑调整' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 'CMS删除操作员逻辑调整' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - [jira_service.py:884] - 子任务 '申请订单页-【申请期限】字段反显sku的配置值' 的主任务 'Web端「个人中心」' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:942] - 子任务创建完成: 成功 0 个，失败 10 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:357] - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:358] - JIRA 批量任务创建完成:
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:359] - 总任务数: 10
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:360] - 主任务创建: 0 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:361] - 子任务创建: 0 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:362] - Sprint关联: 0 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:363] - 需求关联: 0 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:364] - 成功: 0 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:365] - 失败: 15 个
2025-07-10 17:58:01 - jira_api - INFO - [jira_service.py:366] - ================================================================================
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:307] - ================================================================================
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:308] - 开始按层次结构批量创建JIRA任务
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:309] - 任务数量: 8
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:310] - 环境: test
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:311] - 项目: TEST
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:312] - 用户: test_user
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:313] - Sprint: INST2025-sprint10
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:314] - ================================================================================
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:381] - 开始分析任务层次结构...
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:400] - 发现主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:400] - 发现主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:416] - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:418] - 任务结构分析完成: 2 个主任务, 8 个子任务
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:319] - 分析结果: 2 个主任务, 8 个子任务
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:430] - ============================================================
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:431] - 步骤1: 开始创建主任务
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:432] - 需要创建 2 个主任务
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:433] - ============================================================
2025-07-10 18:04:38 - jira_api - INFO - [jira_service.py:437] - 创建主任务: MCP优化
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:552] - ================================================================================
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:553] - JIRA API 创建main_task请求详情:
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:554] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:555] - 请求方法: POST
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:556] - 认证用户: test_user
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:557] - 请求体 (JSON):
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:558] - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "MCP优化",
    "description": "橙卡: JGKEZH-23876\n模块: 《MCP追加第四、五大点》\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "test.qa"
    }
  }
}
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:559] - ================================================================================
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task响应详情:
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:570] - 响应状态码: 502
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:571] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:572] - 响应体:
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:577] - 响应文本: 
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:578] - ================================================================================
2025-07-10 18:04:39 - jira_api - ERROR - [jira_service.py:596] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:04:39 - jira_api - ERROR - [jira_service.py:600] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:04:39 - jira_api - ERROR - [jira_service.py:466] - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:437] - 创建主任务: 用户权限优化
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:552] - ================================================================================
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:553] - JIRA API 创建main_task请求详情:
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:554] - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:555] - 请求方法: POST
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:556] - 认证用户: test_user
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:557] - 请求体 (JSON):
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:558] - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "用户权限优化",
    "description": "橙卡: JGKEZH-24150\n模块: 用户管理模块\n预估工作量: 2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "customfield_11305": {
      "name": "test.qa"
    }
  }
}
2025-07-10 18:04:39 - jira_api - INFO - [jira_service.py:559] - ================================================================================
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:569] - JIRA API 创建main_task响应详情:
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:570] - 响应状态码: 502
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:571] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:572] - 响应体:
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:577] - 响应文本: 
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:578] - ================================================================================
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:596] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:600] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:466] - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:478] - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:760] - ============================================================
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:761] - 步骤3: 开始需求关联
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:762] - ============================================================
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:794] - 需求关联完成: 成功 0 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:852] - ============================================================
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:853] - 步骤4: 开始创建子任务
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:854] - 需要创建 8 个子任务
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:855] - ============================================================
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 'web端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 'cms端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 'MCP优化测试' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 'CMS自动生成【MCP介绍】' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 'mcp服务可用工具列表接口' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 '用户角色管理界面优化' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 '权限验证API接口' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - [jira_service.py:872] - 子任务 '权限功能测试' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:930] - 子任务创建完成: 成功 0 个，失败 8 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:357] - ================================================================================
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:358] - JIRA 批量任务创建完成:
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:359] - 总任务数: 8
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:360] - 主任务创建: 0 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:361] - 子任务创建: 0 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:362] - Sprint关联: 0 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:363] - 需求关联: 0 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:364] - 成功: 0 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:365] - 失败: 10 个
2025-07-10 18:04:40 - jira_api - INFO - [jira_service.py:366] - ================================================================================
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:313] - ================================================================================
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:314] - 开始按层次结构批量创建JIRA任务
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:315] - 任务数量: 8
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:316] - 环境: test
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:317] - 项目: JGKEZH
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:318] - 用户: lidezheng
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:319] - Sprint: INST2025-sprint10
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:320] - ================================================================================
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:387] - 开始分析任务层次结构...
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:406] - 发现主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:406] - 发现主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:424] - 任务结构分析完成: 2 个主任务, 8 个子任务
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:325] - 分析结果: 2 个主任务, 8 个子任务
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:436] - ============================================================
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:437] - 步骤1: 开始创建主任务
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:438] - 需要创建 2 个主任务
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:439] - ============================================================
2025-07-10 18:13:18 - jira_api - INFO - [jira_service.py:443] - 创建主任务: MCP优化
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "MCP优化",
    "description": "橙卡: JGKEZH-23876\n模块: 《MCP追加第四、五大点》\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:13:19 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:13:19 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:13:19 - jira_api - ERROR - [jira_service.py:472] - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:13:19 - jira_api - INFO - [jira_service.py:443] - 创建主任务: 用户权限优化
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "用户权限优化",
    "description": "橙卡: JGKEZH-24150\n模块: 用户管理模块\n预估工作量: 2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:472] - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:484] - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:777] - ============================================================
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:778] - 步骤3: 开始需求关联
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:779] - ============================================================
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:811] - 需求关联完成: 成功 0 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:874] - ============================================================
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:875] - 步骤4: 开始创建子任务
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:876] - 需要创建 8 个子任务
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:877] - 成功创建的主任务数量: 0
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:878] - ============================================================
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 'MCP优化' 创建失败，子任务 'web端 应用文案全部替换为fundx应用' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 'MCP优化' 创建失败，子任务 'cms端 应用文案全部替换为fundx应用' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 'MCP优化' 创建失败，子任务 'MCP优化测试' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 'MCP优化' 创建失败，子任务 'CMS自动生成【MCP介绍】' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 'MCP优化' 创建失败，子任务 'mcp服务可用工具列表接口' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 '用户权限优化' 创建失败，子任务 '用户角色管理界面优化' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 '用户权限优化' 创建失败，子任务 '权限验证API接口' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - [jira_service.py:898] - 主任务 '用户权限优化' 创建失败，子任务 '权限功能测试' 无法创建
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:915] - 有效子任务: 0 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:916] - 因主任务失败而跳过的子任务: 8 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:970] - 子任务创建完成: 成功 0 个，失败 8 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:363] - ================================================================================
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:364] - JIRA 批量任务创建完成:
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:365] - 总任务数: 8
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:366] - 主任务创建: 0 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:367] - 子任务创建: 0 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:368] - Sprint关联: 0 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:369] - 需求关联: 0 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:370] - 成功: 0 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:371] - 失败: 10 个
2025-07-10 18:13:20 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:313] - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:314] - 开始按层次结构批量创建JIRA任务
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:315] - 任务数量: 6
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:316] - 环境: test
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:317] - 项目: JGKEZH
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:318] - 用户: lidezheng
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:319] - Sprint: INST2025-Sprint10
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:320] - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:387] - 开始分析任务层次结构...
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:406] - 发现主任务: CMS图片裁剪功能优化
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:406] - 发现主任务: CMS代办通知邮件
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:406] - 发现主任务: ATP专业系统申请优化
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 提示文案更新 -> 主任务: ATP专业系统申请优化
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 需要开通的极速柜台，改为必填 -> 主任务: ATP专业系统申请优化
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:406] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:422] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:406] - 发现主任务: Web端「个人中心」
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 申请订单页-【申请期限】字段反显sku的配置值 -> 主任务: Web端「个人中心」
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:424] - 任务结构分析完成: 5 个主任务, 6 个子任务
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:325] - 分析结果: 5 个主任务, 6 个子任务
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:436] - ============================================================
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:437] - 步骤1: 开始创建主任务
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:438] - 需要创建 5 个主任务
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:439] - ============================================================
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:443] - 创建主任务: CMS图片裁剪功能优化
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:07 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:15:09 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - [jira_service.py:472] - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:443] - 创建主任务: CMS代办通知邮件
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "wxhuangyichen"
    },
    "customfield_13103": {
      "name": "wxhuangyichen"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:15:09 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - [jira_service.py:472] - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:443] - 创建主任务: ATP专业系统申请优化
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "ATP专业系统申请优化",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:09 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:15:10 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - [jira_service.py:472] - 主任务 'ATP专业系统申请优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:443] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:15:10 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - [jira_service.py:472] - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:443] - 创建主任务: Web端「个人中心」
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "Web端「个人中心」",
    "description": "橙卡: JGKEZH-23514\n模块: 开放广发专区优化\n预估工作量: 0.2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:10 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:472] - 主任务 'Web端「个人中心」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:484] - 主任务创建完成: 成功 0 个，失败 5 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:777] - ============================================================
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:778] - 步骤3: 开始需求关联
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:779] - ============================================================
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:811] - 需求关联完成: 成功 0 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:874] - ============================================================
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:875] - 步骤4: 开始创建子任务
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:876] - 需要创建 6 个子任务
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:877] - 成功创建的主任务数量: 0
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:878] - ============================================================
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:898] - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:898] - 主任务 'CMS代办通知邮件' 创建失败，子任务 '发送邮件模板通知调整' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:898] - 主任务 'ATP专业系统申请优化' 创建失败，子任务 '提示文案更新' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:898] - 主任务 'ATP专业系统申请优化' 创建失败，子任务 '需要开通的极速柜台，改为必填' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:898] - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'cms审核支持退回' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - [jira_service.py:898] - 主任务 'Web端「个人中心」' 创建失败，子任务 '申请订单页-【申请期限】字段反显sku的配置值' 无法创建
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:915] - 有效子任务: 0 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:916] - 因主任务失败而跳过的子任务: 6 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:970] - 子任务创建完成: 成功 0 个，失败 6 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:363] - ================================================================================
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:364] - JIRA 批量任务创建完成:
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:365] - 总任务数: 6
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:366] - 主任务创建: 0 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:367] - 子任务创建: 0 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:368] - Sprint关联: 0 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:369] - 需求关联: 0 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:370] - 成功: 0 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:371] - 失败: 11 个
2025-07-10 18:15:16 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:313] - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:314] - 开始按层次结构批量创建JIRA任务
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:315] - 任务数量: 11
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:316] - 环境: test
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:317] - 项目: JGKEZH
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:318] - 用户: lidezheng
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:319] - Sprint: INST2025-Sprint10
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:320] - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:387] - 开始分析任务层次结构...
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:406] - 发现主任务: CMS图片裁剪功能优化
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:406] - 发现主任务: CMS代办通知邮件
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:406] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:406] - 发现主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:406] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:422] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:424] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:325] - 分析结果: 5 个主任务, 11 个子任务
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:436] - ============================================================
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:437] - 步骤1: 开始创建主任务
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:438] - 需要创建 5 个主任务
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:439] - ============================================================
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:443] - 创建主任务: CMS图片裁剪功能优化
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:29 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:472] - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:443] - 创建主任务: CMS代办通知邮件
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:472] - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:443] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - [jira_service.py:472] - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:443] - 创建主任务: CMS端「产品目录树」
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "description": "橙卡: JGKEZH-2410\n模块: 三季度优化\n预估工作量: 1.0d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:30 - jira_api - INFO - [jira_service.py:569] - ================================================================================
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:579] - JIRA API 创建main_task响应详情:
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:580] - 响应状态码: 502
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:581] - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:582] - 响应体:
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:587] - 响应文本: 
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:588] - ================================================================================
2025-07-10 18:18:31 - jira_api - ERROR - [jira_service.py:606] - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - ERROR - [jira_service.py:610] - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - ERROR - [jira_service.py:472] - 主任务 'CMS端「产品目录树」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:443] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:562] - ================================================================================
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:563] - JIRA API 创建main_task请求详情:
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:564] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:565] - 请求方法: POST
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:566] - 认证用户: lidezheng
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:567] - 请求体 (JSON):
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:568] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "description": "橙卡: JGKEZH-2410\n模块: 三季度优化\n预估工作量: 0.2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:31 - jira_api - INFO - [jira_service.py:569] - ================================================================================
