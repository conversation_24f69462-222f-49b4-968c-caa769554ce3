2025-07-10 17:27:45 - root - INFO - 日志系统初始化完成
2025-07-10 17:27:45 - root - INFO - 日志级别: INFO
2025-07-10 17:27:45 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:27:45 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:30:41 - root - INFO - 日志系统初始化完成
2025-07-10 17:30:41 - root - INFO - 日志级别: INFO
2025-07-10 17:30:41 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:30:41 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:30:41 - app.services.jira_service - INFO - ================================================================================
2025-07-10 17:30:41 - app.services.jira_service - INFO - JIRA 连接测试详情:
2025-07-10 17:30:41 - app.services.jira_service - INFO - 测试URL: http://jirauat.gf.com.cn/rest/api/2/myself
2025-07-10 17:30:41 - app.services.jira_service - INFO - 请求方法: GET
2025-07-10 17:30:41 - app.services.jira_service - INFO - 认证用户: test_user
2025-07-10 17:30:41 - app.services.jira_service - INFO - 环境: test
2025-07-10 17:30:41 - app.services.jira_service - INFO - ================================================================================
2025-07-10 17:30:43 - app.services.jira_service - INFO - JIRA 连接测试响应:
2025-07-10 17:30:43 - app.services.jira_service - INFO - 响应状态码: 502
2025-07-10 17:30:43 - app.services.jira_service - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:30:43 - app.services.jira_service - INFO - 响应体:
2025-07-10 17:30:43 - app.services.jira_service - INFO - 响应文本: 
2025-07-10 17:30:43 - app.services.jira_service - INFO - ================================================================================
2025-07-10 17:30:43 - app.services.jira_service - ERROR - JIRA连接测试失败: 连接测试失败: HTTP 502 - 
2025-07-10 17:31:53 - root - INFO - 日志系统初始化完成
2025-07-10 17:31:53 - root - INFO - 日志级别: INFO
2025-07-10 17:31:53 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:31:53 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:32:10 - root - INFO - 日志系统初始化完成
2025-07-10 17:32:10 - root - INFO - 日志级别: INFO
2025-07-10 17:32:10 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:32:10 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:32:26 - root - INFO - 日志系统初始化完成
2025-07-10 17:32:26 - root - INFO - 日志级别: INFO
2025-07-10 17:32:26 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:32:26 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:32:48 - root - INFO - 日志系统初始化完成
2025-07-10 17:32:48 - root - INFO - 日志级别: INFO
2025-07-10 17:32:48 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:32:48 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:32:48 - jira_api - INFO - ================================================================================
2025-07-10 17:32:48 - jira_api - INFO - JIRA 连接测试详情:
2025-07-10 17:32:48 - jira_api - INFO - 测试URL: http://jirauat.gf.com.cn/rest/api/2/myself
2025-07-10 17:32:48 - jira_api - INFO - 请求方法: GET
2025-07-10 17:32:48 - jira_api - INFO - 认证用户: test_user
2025-07-10 17:32:48 - jira_api - INFO - 环境: test
2025-07-10 17:32:48 - jira_api - INFO - ================================================================================
2025-07-10 17:32:48 - jira_api - INFO - JIRA 连接测试响应:
2025-07-10 17:32:48 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:32:48 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:32:48 - jira_api - INFO - 响应体:
2025-07-10 17:32:48 - jira_api - INFO - 响应文本: 
2025-07-10 17:32:48 - jira_api - INFO - ================================================================================
2025-07-10 17:32:48 - app.services.jira_service - ERROR - JIRA连接测试失败: 连接测试失败: HTTP 502 - 
2025-07-10 17:37:18 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代', '代码分支', '任务依赖', '后端接口文档提供时间', '备注']
2025-07-10 17:37:18 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 17:37:18 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务             子任务 任务类型  负责人  工作量                 迭代  代码分支  任务依赖  后端接口文档提供时间  备注
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化    多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10   NaN   NaN         NaN NaN
1  JGKEZH-24332  三季度优化        CMS代办通知邮件      发送邮件模板通知调整   后端  黄翌晨  0.3  INST2025-sprint10   NaN   NaN         NaN NaN
2  JGKEZH-24332  三季度优化      ATP专业系统申请优化          提示文案更新   UI   关远  0.1  INST2025-sprint10   NaN   NaN         NaN NaN
3  JGKEZH-24332  三季度优化      ATP专业系统申请优化  需要开通的极速柜台，改为必填   UI   关远  0.2  INST2025-sprint10   NaN   NaN         NaN NaN
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请       cms审核支持退回   UI   关远  0.1  INST2025-sprint10   NaN   NaN         NaN NaN
2025-07-10 17:38:05 - root - INFO - ================================================================================
2025-07-10 17:38:05 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-10 17:38:05 - root - INFO - 任务数量: 4
2025-07-10 17:38:05 - root - INFO - 环境: test
2025-07-10 17:38:05 - root - INFO - 用户: lidezheng
2025-07-10 17:38:05 - root - INFO - 项目: JGKEZH
2025-07-10 17:38:05 - root - INFO - Sprint: INST2025-Sprint10
2025-07-10 17:38:05 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-10 17:38:05 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10"
}
2025-07-10 17:38:05 - root - INFO - 任务详情:
2025-07-10 17:38:05 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-10 17:38:05 - root - INFO -   任务 2: 发送邮件模板通知调整 - 负责人: 黄翌晨
2025-07-10 17:38:05 - root - INFO -   任务 3: 提示文案更新 - 负责人: 关远
2025-07-10 17:38:05 - root - INFO -   任务 4: 需要开通的极速柜台，改为必填 - 负责人: 关远
2025-07-10 17:38:05 - root - INFO - ================================================================================
2025-07-10 17:38:05 - app.services.jira_service - INFO - ================================================================================
2025-07-10 17:38:05 - app.services.jira_service - INFO - JIRA 批量任务创建开始:
2025-07-10 17:38:05 - app.services.jira_service - INFO - 任务数量: 4
2025-07-10 17:38:05 - app.services.jira_service - INFO - 环境: test
2025-07-10 17:38:05 - app.services.jira_service - INFO - 项目: JGKEZH
2025-07-10 17:38:05 - app.services.jira_service - INFO - 用户: lidezheng
2025-07-10 17:38:05 - app.services.jira_service - INFO - Sprint: INST2025-Sprint10
2025-07-10 17:38:05 - app.services.jira_service - INFO - 任务列表:
2025-07-10 17:38:05 - app.services.jira_service - INFO -   1. 多个场景设定图片上传比例 - 关远
2025-07-10 17:38:05 - app.services.jira_service - INFO -   2. 发送邮件模板通知调整 - 黄翌晨
2025-07-10 17:38:05 - app.services.jira_service - INFO -   3. 提示文案更新 - 关远
2025-07-10 17:38:05 - app.services.jira_service - INFO -   4. 需要开通的极速柜台，改为必填 - 关远
2025-07-10 17:38:05 - app.services.jira_service - INFO - ================================================================================
2025-07-10 17:38:05 - app.services.jira_service - INFO - 创建任务 1/4: 多个场景设定图片上传比例
2025-07-10 17:38:05 - jira_api - INFO - ================================================================================
2025-07-10 17:38:05 - jira_api - INFO - JIRA API 请求详情:
2025-07-10 17:38:05 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:05 - jira_api - INFO - 请求方法: POST
2025-07-10 17:38:05 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:38:05 - jira_api - INFO - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:05 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:38:05 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: CMS图片裁剪功能优化\n预估工作量: 0.3d\n任务类型: UI"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    },
    "assignee": {
      "name": "guanyuan"
    }
  }
}
2025-07-10 17:38:05 - jira_api - INFO - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - JIRA API 响应详情:
2025-07-10 17:38:06 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:38:06 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:06 - jira_api - INFO - 响应体:
2025-07-10 17:38:06 - jira_api - INFO - 响应文本: 
2025-07-10 17:38:06 - jira_api - INFO - ================================================================================
2025-07-10 17:38:06 - app.services.jira_service - ERROR - JIRA API 调用失败:
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 状态码: 502
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 错误消息: JIRA API错误: 502 - 
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 响应文本: 
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 创建JIRA任务异常: JIRA API错误: 502 - 
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 任务 1 创建失败: JIRA API错误: 502 - 
2025-07-10 17:38:06 - app.services.jira_service - INFO - 创建任务 2/4: 发送邮件模板通知调整
2025-07-10 17:38:06 - app.services.jira_service - WARNING - 未找到姓名 '黄翌晨' 对应的OA账号
2025-07-10 17:38:06 - app.services.jira_service - WARNING - 负责人 '黄翌晨' 未找到对应的OA账号，将使用原始姓名
2025-07-10 17:38:06 - jira_api - INFO - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - JIRA API 请求详情:
2025-07-10 17:38:06 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:06 - jira_api - INFO - 请求方法: POST
2025-07-10 17:38:06 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:38:06 - jira_api - INFO - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:06 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:38:06 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: CMS代办通知邮件\n预估工作量: 0.3d\n任务类型: 后端"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    }
  }
}
2025-07-10 17:38:06 - jira_api - INFO - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - JIRA API 响应详情:
2025-07-10 17:38:06 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:38:06 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:06 - jira_api - INFO - 响应体:
2025-07-10 17:38:06 - jira_api - INFO - 响应文本: 
2025-07-10 17:38:06 - jira_api - INFO - ================================================================================
2025-07-10 17:38:06 - app.services.jira_service - ERROR - JIRA API 调用失败:
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 状态码: 502
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 错误消息: JIRA API错误: 502 - 
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 响应文本: 
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 创建JIRA任务异常: JIRA API错误: 502 - 
2025-07-10 17:38:06 - app.services.jira_service - ERROR - 任务 2 创建失败: JIRA API错误: 502 - 
2025-07-10 17:38:06 - app.services.jira_service - INFO - 创建任务 3/4: 提示文案更新
2025-07-10 17:38:06 - jira_api - INFO - ================================================================================
2025-07-10 17:38:06 - jira_api - INFO - JIRA API 请求详情:
2025-07-10 17:38:06 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:06 - jira_api - INFO - 请求方法: POST
2025-07-10 17:38:06 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:38:06 - jira_api - INFO - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:06 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:38:06 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "提示文案更新",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: ATP专业系统申请优化\n预估工作量: 0.1d\n任务类型: UI"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    },
    "assignee": {
      "name": "guanyuan"
    }
  }
}
2025-07-10 17:38:06 - jira_api - INFO - ================================================================================
2025-07-10 17:38:07 - jira_api - INFO - JIRA API 响应详情:
2025-07-10 17:38:07 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:38:07 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:07 - jira_api - INFO - 响应体:
2025-07-10 17:38:07 - jira_api - INFO - 响应文本: 
2025-07-10 17:38:07 - jira_api - INFO - ================================================================================
2025-07-10 17:38:07 - app.services.jira_service - ERROR - JIRA API 调用失败:
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 状态码: 502
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 错误消息: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 响应文本: 
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 创建JIRA任务异常: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 任务 3 创建失败: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - INFO - 创建任务 4/4: 需要开通的极速柜台，改为必填
2025-07-10 17:38:07 - jira_api - INFO - ================================================================================
2025-07-10 17:38:07 - jira_api - INFO - JIRA API 请求详情:
2025-07-10 17:38:07 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:38:07 - jira_api - INFO - 请求方法: POST
2025-07-10 17:38:07 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:38:07 - jira_api - INFO - 请求头: {'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'}
2025-07-10 17:38:07 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:38:07 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "需要开通的极速柜台，改为必填",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n主任务: ATP专业系统申请优化\n预估工作量: 0.2d\n任务类型: UI"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "name": "Task"
    },
    "assignee": {
      "name": "guanyuan"
    }
  }
}
2025-07-10 17:38:07 - jira_api - INFO - ================================================================================
2025-07-10 17:38:07 - jira_api - INFO - JIRA API 响应详情:
2025-07-10 17:38:07 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:38:07 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:38:07 - jira_api - INFO - 响应体:
2025-07-10 17:38:07 - jira_api - INFO - 响应文本: 
2025-07-10 17:38:07 - jira_api - INFO - ================================================================================
2025-07-10 17:38:07 - app.services.jira_service - ERROR - JIRA API 调用失败:
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 状态码: 502
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 错误消息: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 响应文本: 
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 创建JIRA任务异常: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - ERROR - 任务 4 创建失败: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - INFO - ================================================================================
2025-07-10 17:38:07 - app.services.jira_service - INFO - JIRA 批量任务创建完成:
2025-07-10 17:38:07 - app.services.jira_service - INFO - 总任务数: 4
2025-07-10 17:38:07 - app.services.jira_service - INFO - 成功: 0 个
2025-07-10 17:38:07 - app.services.jira_service - INFO - 失败: 4 个
2025-07-10 17:38:07 - app.services.jira_service - INFO - 失败的任务:
2025-07-10 17:38:07 - app.services.jira_service - INFO -   - 多个场景设定图片上传比例: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - INFO -   - 发送邮件模板通知调整: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - INFO -   - 提示文案更新: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - INFO -   - 需要开通的极速柜台，改为必填: JIRA API错误: 502 - 
2025-07-10 17:38:07 - app.services.jira_service - INFO - ================================================================================
2025-07-10 17:38:07 - root - INFO - ================================================================================
2025-07-10 17:38:07 - root - INFO - JIRA批量任务创建结果:
2025-07-10 17:38:07 - root - INFO - 总任务数: 4
2025-07-10 17:38:07 - root - INFO - 成功: 0 个
2025-07-10 17:38:07 - root - INFO - 失败: 4 个
2025-07-10 17:38:07 - root - INFO - 返回结果:
2025-07-10 17:38:07 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "error": "JIRA API错误: 502 - ",
        "title": "多个场景设定图片上传比例",
        "assignee": "关远",
        "task_type": "UI",
        "environment": "test",
        "sprint": "INST2025-Sprint10"
      },
      {
        "success": false,
        "error": "JIRA API错误: 502 - ",
        "title": "发送邮件模板通知调整",
        "assignee": "黄翌晨",
        "task_type": "后端",
        "environment": "test",
        "sprint": "INST2025-Sprint10"
      },
      {
        "success": false,
        "error": "JIRA API错误: 502 - ",
        "title": "提示文案更新",
        "assignee": "关远",
        "task_type": "UI",
        "environment": "test",
        "sprint": "INST2025-Sprint10"
      },
      {
        "success": false,
        "error": "JIRA API错误: 502 - ",
        "title": "需要开通的极速柜台，改为必填",
        "assignee": "关远",
        "task_type": "UI",
        "environment": "test",
        "sprint": "INST2025-Sprint10"
      }
    ],
    "summary": {
      "total": 4,
      "success_count": 0,
      "failed_count": 4
    }
  }
}
2025-07-10 17:38:07 - root - INFO - ================================================================================
2025-07-10 17:45:56 - root - INFO - 日志系统初始化完成
2025-07-10 17:45:56 - root - INFO - 日志级别: INFO
2025-07-10 17:45:56 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:45:56 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:46:21 - root - INFO - 日志系统初始化完成
2025-07-10 17:46:21 - root - INFO - 日志级别: INFO
2025-07-10 17:46:21 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:46:21 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:46:46 - root - INFO - 日志系统初始化完成
2025-07-10 17:46:46 - root - INFO - 日志级别: INFO
2025-07-10 17:46:46 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:46:46 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:47:11 - root - INFO - 日志系统初始化完成
2025-07-10 17:47:11 - root - INFO - 日志级别: INFO
2025-07-10 17:47:11 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:47:11 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:47:38 - root - INFO - 日志系统初始化完成
2025-07-10 17:47:38 - root - INFO - 日志级别: INFO
2025-07-10 17:47:38 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:47:38 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:48:05 - root - INFO - 日志系统初始化完成
2025-07-10 17:48:05 - root - INFO - 日志级别: INFO
2025-07-10 17:48:05 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:48:05 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:48:34 - root - INFO - 日志系统初始化完成
2025-07-10 17:48:34 - root - INFO - 日志级别: INFO
2025-07-10 17:48:34 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:48:34 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:49:01 - root - INFO - 日志系统初始化完成
2025-07-10 17:49:01 - root - INFO - 日志级别: INFO
2025-07-10 17:49:01 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:49:01 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:49:33 - root - INFO - 日志系统初始化完成
2025-07-10 17:49:33 - root - INFO - 日志级别: INFO
2025-07-10 17:49:33 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:49:33 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:50:01 - root - INFO - 日志系统初始化完成
2025-07-10 17:50:01 - root - INFO - 日志级别: INFO
2025-07-10 17:50:01 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:50:01 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:52:16 - root - INFO - 日志系统初始化完成
2025-07-10 17:52:16 - root - INFO - 日志级别: INFO
2025-07-10 17:52:16 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:52:16 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:54:53 - root - INFO - 日志系统初始化完成
2025-07-10 17:54:53 - root - INFO - 日志级别: INFO
2025-07-10 17:54:53 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 17:54:53 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 17:54:53 - jira_api - INFO - ================================================================================
2025-07-10 17:54:53 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 17:54:53 - jira_api - INFO - 任务数量: 8
2025-07-10 17:54:53 - jira_api - INFO - 环境: test
2025-07-10 17:54:53 - jira_api - INFO - 项目: TEST
2025-07-10 17:54:53 - jira_api - INFO - 用户: test_user
2025-07-10 17:54:53 - jira_api - INFO - Sprint: INST2025-sprint10
2025-07-10 17:54:53 - jira_api - INFO - ================================================================================
2025-07-10 17:54:53 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 17:54:53 - jira_api - INFO - 发现主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-10 17:54:53 - jira_api - INFO - 发现主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-10 17:54:53 - jira_api - INFO - 任务结构分析完成: 2 个主任务, 8 个子任务
2025-07-10 17:54:53 - jira_api - INFO - 分析结果: 2 个主任务, 8 个子任务
2025-07-10 17:54:53 - jira_api - INFO - ============================================================
2025-07-10 17:54:53 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 17:54:53 - jira_api - INFO - 需要创建 2 个主任务
2025-07-10 17:54:53 - jira_api - INFO - ============================================================
2025-07-10 17:54:53 - jira_api - INFO - 创建主任务: MCP优化
2025-07-10 17:54:54 - jira_api - INFO - ================================================================================
2025-07-10 17:54:54 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 17:54:54 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:54:54 - jira_api - INFO - 请求方法: POST
2025-07-10 17:54:54 - jira_api - INFO - 认证用户: test_user
2025-07-10 17:54:54 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:54:54 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "MCP优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-23876\n模块: 《MCP追加第四、五大点》\n预估工作量: 0.3d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:54:54 - jira_api - INFO - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 17:54:55 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:54:55 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:54:55 - jira_api - INFO - 响应体:
2025-07-10 17:54:55 - jira_api - INFO - 响应文本: 
2025-07-10 17:54:55 - jira_api - INFO - ================================================================================
2025-07-10 17:54:55 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - INFO - 创建主任务: 用户权限优化
2025-07-10 17:54:55 - app.services.jira_service - WARNING - 未找到姓名 '张三' 对应的OA账号
2025-07-10 17:54:55 - jira_api - INFO - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 17:54:55 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:54:55 - jira_api - INFO - 请求方法: POST
2025-07-10 17:54:55 - jira_api - INFO - 认证用户: test_user
2025-07-10 17:54:55 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:54:55 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "用户权限优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24150\n模块: 用户管理模块\n预估工作量: 2d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:54:55 - jira_api - INFO - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 17:54:55 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:54:55 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:54:55 - jira_api - INFO - 响应体:
2025-07-10 17:54:55 - jira_api - INFO - 响应文本: 
2025-07-10 17:54:55 - jira_api - INFO - ================================================================================
2025-07-10 17:54:55 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:54:55 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-10 17:54:55 - jira_api - INFO - ============================================================
2025-07-10 17:54:55 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 17:54:55 - jira_api - INFO - ============================================================
2025-07-10 17:54:55 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 17:54:55 - jira_api - INFO - ============================================================
2025-07-10 17:54:55 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 17:54:55 - jira_api - INFO - 需要创建 8 个子任务
2025-07-10 17:54:55 - jira_api - INFO - ============================================================
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 'web端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 'cms端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 'MCP优化测试' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 'CMS自动生成【MCP介绍】' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 'mcp服务可用工具列表接口' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 '用户角色管理界面优化' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 '权限验证API接口' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - ERROR - 子任务 '权限功能测试' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 17:54:55 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 8 个
2025-07-10 17:54:55 - jira_api - INFO - ================================================================================
2025-07-10 17:54:55 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 17:54:55 - jira_api - INFO - 总任务数: 8
2025-07-10 17:54:55 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 17:54:55 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 17:54:55 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 17:54:55 - jira_api - INFO - 需求关联: 0 个
2025-07-10 17:54:55 - jira_api - INFO - 成功: 0 个
2025-07-10 17:54:55 - jira_api - INFO - 失败: 10 个
2025-07-10 17:54:55 - jira_api - INFO - ================================================================================
2025-07-10 17:57:20 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代', '代码分支', '任务依赖', '后端接口文档提供时间', '备注']
2025-07-10 17:57:20 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 17:57:20 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务             子任务 任务类型  负责人  工作量                 迭代  代码分支  任务依赖  后端接口文档提供时间  备注
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化    多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10   NaN   NaN         NaN NaN
1  JGKEZH-24332  三季度优化        CMS代办通知邮件      发送邮件模板通知调整   后端  黄翌晨  0.3  INST2025-sprint10   NaN   NaN         NaN NaN
2  JGKEZH-24332  三季度优化      ATP专业系统申请优化          提示文案更新   UI   关远  0.1  INST2025-sprint10   NaN   NaN         NaN NaN
3  JGKEZH-24332  三季度优化      ATP专业系统申请优化  需要开通的极速柜台，改为必填   UI   关远  0.2  INST2025-sprint10   NaN   NaN         NaN NaN
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请       cms审核支持退回   UI   关远  0.1  INST2025-sprint10   NaN   NaN         NaN NaN
2025-07-10 17:57:58 - root - INFO - ================================================================================
2025-07-10 17:57:58 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-10 17:57:58 - root - INFO - 任务数量: 10
2025-07-10 17:57:58 - root - INFO - 环境: test
2025-07-10 17:57:58 - root - INFO - 用户: lidezheng
2025-07-10 17:57:58 - root - INFO - 项目: JGKEZH
2025-07-10 17:57:58 - root - INFO - Sprint: INST2025-Sprint10
2025-07-10 17:57:58 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-10 17:57:58 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10"
}
2025-07-10 17:57:58 - root - INFO - 任务详情:
2025-07-10 17:57:58 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-10 17:57:58 - root - INFO -   任务 2: 发送邮件模板通知调整 - 负责人: 黄翌晨
2025-07-10 17:57:58 - root - INFO -   任务 3: 提示文案更新 - 负责人: 关远
2025-07-10 17:57:58 - root - INFO -   任务 4: 需要开通的极速柜台，改为必填 - 负责人: 关远
2025-07-10 17:57:58 - root - INFO -   任务 5: cms审核支持退回 - 负责人: 关远
2025-07-10 17:57:58 - root - INFO -   任务 6: web端退回申请的展示 - 负责人: 关远
2025-07-10 17:57:58 - root - INFO -   任务 7: cms端退回申请的展示 - 负责人: 关远
2025-07-10 17:57:58 - root - INFO -   任务 8: web删除操作员逻辑调整 - 负责人: 黄翌晨
2025-07-10 17:57:58 - root - INFO -   任务 9: CMS删除操作员逻辑调整 - 负责人: 黄翌晨
2025-07-10 17:57:58 - root - INFO -   任务 10: 申请订单页-【申请期限】字段反显sku的配置值 - 负责人: 耿庆岩
2025-07-10 17:57:58 - root - INFO - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 17:57:58 - jira_api - INFO - 任务数量: 10
2025-07-10 17:57:58 - jira_api - INFO - 环境: test
2025-07-10 17:57:58 - jira_api - INFO - 项目: JGKEZH
2025-07-10 17:57:58 - jira_api - INFO - 用户: lidezheng
2025-07-10 17:57:58 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-10 17:57:58 - jira_api - INFO - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 17:57:58 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 17:57:58 - jira_api - INFO - 发现主任务: CMS代办通知邮件
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-10 17:57:58 - jira_api - INFO - 发现主任务: ATP专业系统申请优化
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: 提示文案更新 -> 主任务: ATP专业系统申请优化
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: 需要开通的极速柜台，改为必填 -> 主任务: ATP专业系统申请优化
2025-07-10 17:57:58 - jira_api - INFO - 发现主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: web端退回申请的展示 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: cms端退回申请的展示 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 17:57:58 - jira_api - INFO - 发现主任务: Web端「个人中心」
2025-07-10 17:57:58 - jira_api - INFO - 发现子任务: 申请订单页-【申请期限】字段反显sku的配置值 -> 主任务: Web端「个人中心」
2025-07-10 17:57:58 - jira_api - INFO - 任务结构分析完成: 5 个主任务, 10 个子任务
2025-07-10 17:57:58 - jira_api - INFO - 分析结果: 5 个主任务, 10 个子任务
2025-07-10 17:57:58 - jira_api - INFO - ============================================================
2025-07-10 17:57:58 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 17:57:58 - jira_api - INFO - 需要创建 5 个主任务
2025-07-10 17:57:58 - jira_api - INFO - ============================================================
2025-07-10 17:57:58 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-10 17:57:58 - jira_api - INFO - ================================================================================
2025-07-10 17:57:58 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 17:57:58 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:57:58 - jira_api - INFO - 请求方法: POST
2025-07-10 17:57:58 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:57:58 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:57:58 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:57:58 - jira_api - INFO - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 17:57:59 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:57:59 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:57:59 - jira_api - INFO - 响应体:
2025-07-10 17:57:59 - jira_api - INFO - 响应文本: 
2025-07-10 17:57:59 - jira_api - INFO - ================================================================================
2025-07-10 17:57:59 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - INFO - 创建主任务: CMS代办通知邮件
2025-07-10 17:57:59 - jira_api - INFO - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 17:57:59 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:57:59 - jira_api - INFO - 请求方法: POST
2025-07-10 17:57:59 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:57:59 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:57:59 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "wxhuangyichen"
    },
    "customfield_13103": {
      "name": "wxhuangyichen"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:57:59 - jira_api - INFO - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 17:57:59 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:57:59 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:57:59 - jira_api - INFO - 响应体:
2025-07-10 17:57:59 - jira_api - INFO - 响应文本: 
2025-07-10 17:57:59 - jira_api - INFO - ================================================================================
2025-07-10 17:57:59 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - ERROR - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:57:59 - jira_api - INFO - 创建主任务: ATP专业系统申请优化
2025-07-10 17:57:59 - jira_api - INFO - ================================================================================
2025-07-10 17:57:59 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 17:57:59 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:57:59 - jira_api - INFO - 请求方法: POST
2025-07-10 17:57:59 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:57:59 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:57:59 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "ATP专业系统申请优化",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:57:59 - jira_api - INFO - ================================================================================
2025-07-10 17:58:00 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 17:58:00 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:58:00 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:58:00 - jira_api - INFO - 响应体:
2025-07-10 17:58:00 - jira_api - INFO - 响应文本: 
2025-07-10 17:58:00 - jira_api - INFO - ================================================================================
2025-07-10 17:58:00 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:58:00 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:58:00 - jira_api - ERROR - 主任务 'ATP专业系统申请优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:58:00 - jira_api - INFO - 创建主任务: PB系统支持退回删除操作员申请
2025-07-10 17:58:00 - jira_api - INFO - ================================================================================
2025-07-10 17:58:00 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 17:58:00 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:58:00 - jira_api - INFO - 请求方法: POST
2025-07-10 17:58:00 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:58:00 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:58:00 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:58:00 - jira_api - INFO - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 17:58:01 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:58:01 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:58:01 - jira_api - INFO - 响应体:
2025-07-10 17:58:01 - jira_api - INFO - 响应文本: 
2025-07-10 17:58:01 - jira_api - INFO - ================================================================================
2025-07-10 17:58:01 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - INFO - 创建主任务: Web端「个人中心」
2025-07-10 17:58:01 - app.services.jira_service - WARNING - 未找到姓名 '耿庆岩' 对应的OA账号
2025-07-10 17:58:01 - jira_api - INFO - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 17:58:01 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 17:58:01 - jira_api - INFO - 请求方法: POST
2025-07-10 17:58:01 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 17:58:01 - jira_api - INFO - 请求体 (JSON):
2025-07-10 17:58:01 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "Web端「个人中心」",
    "description": {
      "type": "doc",
      "version": 1,
      "content": [
        {
          "type": "paragraph",
          "content": [
            {
              "type": "text",
              "text": "橙卡: JGKEZH-23514\n模块: 开放广发专区优化\n预估工作量: 0.2d"
            }
          ]
        }
      ]
    },
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 17:58:01 - jira_api - INFO - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 17:58:01 - jira_api - INFO - 响应状态码: 502
2025-07-10 17:58:01 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 17:58:01 - jira_api - INFO - 响应体:
2025-07-10 17:58:01 - jira_api - INFO - 响应文本: 
2025-07-10 17:58:01 - jira_api - INFO - ================================================================================
2025-07-10 17:58:01 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - ERROR - 主任务 'Web端「个人中心」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 17:58:01 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 5 个
2025-07-10 17:58:01 - jira_api - INFO - ============================================================
2025-07-10 17:58:01 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 17:58:01 - jira_api - INFO - ============================================================
2025-07-10 17:58:01 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 17:58:01 - jira_api - INFO - ============================================================
2025-07-10 17:58:01 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 17:58:01 - jira_api - INFO - 需要创建 10 个子任务
2025-07-10 17:58:01 - jira_api - INFO - ============================================================
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 '多个场景设定图片上传比例' 的主任务 'CMS图片裁剪功能优化' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 '发送邮件模板通知调整' 的主任务 'CMS代办通知邮件' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 '提示文案更新' 的主任务 'ATP专业系统申请优化' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 '需要开通的极速柜台，改为必填' 的主任务 'ATP专业系统申请优化' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 'cms审核支持退回' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 'web端退回申请的展示' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 'cms端退回申请的展示' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 'web删除操作员逻辑调整' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 'CMS删除操作员逻辑调整' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - ERROR - 子任务 '申请订单页-【申请期限】字段反显sku的配置值' 的主任务 'Web端「个人中心」' 未找到对应的JIRA Key
2025-07-10 17:58:01 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 10 个
2025-07-10 17:58:01 - jira_api - INFO - ================================================================================
2025-07-10 17:58:01 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 17:58:01 - jira_api - INFO - 总任务数: 10
2025-07-10 17:58:01 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 17:58:01 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 17:58:01 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 17:58:01 - jira_api - INFO - 需求关联: 0 个
2025-07-10 17:58:01 - jira_api - INFO - 成功: 0 个
2025-07-10 17:58:01 - jira_api - INFO - 失败: 15 个
2025-07-10 17:58:01 - jira_api - INFO - ================================================================================
2025-07-10 17:58:01 - root - INFO - ================================================================================
2025-07-10 17:58:01 - root - INFO - JIRA批量任务创建结果:
2025-07-10 17:58:01 - root - INFO - 总任务数: 10
2025-07-10 17:58:01 - root - INFO - 主任务创建: 0 个
2025-07-10 17:58:01 - root - INFO - 子任务创建: 0 个
2025-07-10 17:58:01 - root - INFO - Sprint关联: 0 个
2025-07-10 17:58:01 - root - INFO - 需求关联: 0 个
2025-07-10 17:58:01 - root - INFO - 成功: 0 个
2025-07-10 17:58:01 - root - INFO - 失败: 15 个
2025-07-10 17:58:01 - root - INFO - 返回结果:
2025-07-10 17:58:01 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "type": "main_task",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "CMS代办通知邮件",
        "assignee": "黄翌晨",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "ATP专业系统申请优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "Web端「个人中心」",
        "assignee": "耿庆岩",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "多个场景设定图片上传比例",
        "main_task": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "error": "子任务 '多个场景设定图片上传比例' 的主任务 'CMS图片裁剪功能优化' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "发送邮件模板通知调整",
        "main_task": "CMS代办通知邮件",
        "assignee": "黄翌晨",
        "error": "子任务 '发送邮件模板通知调整' 的主任务 'CMS代办通知邮件' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "提示文案更新",
        "main_task": "ATP专业系统申请优化",
        "assignee": "关远",
        "error": "子任务 '提示文案更新' 的主任务 'ATP专业系统申请优化' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "需要开通的极速柜台，改为必填",
        "main_task": "ATP专业系统申请优化",
        "assignee": "关远",
        "error": "子任务 '需要开通的极速柜台，改为必填' 的主任务 'ATP专业系统申请优化' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "cms审核支持退回",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "error": "子任务 'cms审核支持退回' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "web端退回申请的展示",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "error": "子任务 'web端退回申请的展示' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "cms端退回申请的展示",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "error": "子任务 'cms端退回申请的展示' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "web删除操作员逻辑调整",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "黄翌晨",
        "error": "子任务 'web删除操作员逻辑调整' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "CMS删除操作员逻辑调整",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "黄翌晨",
        "error": "子任务 'CMS删除操作员逻辑调整' 的主任务 'PB系统支持退回删除操作员申请' 未找到对应的JIRA Key"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "申请订单页-【申请期限】字段反显sku的配置值",
        "main_task": "Web端「个人中心」",
        "assignee": "耿庆岩",
        "error": "子任务 '申请订单页-【申请期限】字段反显sku的配置值' 的主任务 'Web端「个人中心」' 未找到对应的JIRA Key"
      }
    ],
    "summary": {
      "total": 10,
      "success_count": 0,
      "failed_count": 15,
      "main_tasks_created": 0,
      "sub_tasks_created": 0,
      "sprint_linked": 0,
      "demands_linked": 0
    }
  }
}
2025-07-10 17:58:01 - root - INFO - ================================================================================
2025-07-10 18:00:57 - root - INFO - 日志系统初始化完成
2025-07-10 18:00:57 - root - INFO - 日志级别: INFO
2025-07-10 18:00:57 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:00:57 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:01:16 - root - INFO - 日志系统初始化完成
2025-07-10 18:01:16 - root - INFO - 日志级别: INFO
2025-07-10 18:01:16 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:01:16 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:01:40 - root - INFO - 日志系统初始化完成
2025-07-10 18:01:40 - root - INFO - 日志级别: INFO
2025-07-10 18:01:40 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:01:40 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:04:10 - root - INFO - 日志系统初始化完成
2025-07-10 18:04:10 - root - INFO - 日志级别: INFO
2025-07-10 18:04:10 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:04:10 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:04:38 - root - INFO - 日志系统初始化完成
2025-07-10 18:04:38 - root - INFO - 日志级别: INFO
2025-07-10 18:04:38 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:04:38 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:04:38 - jira_api - INFO - ================================================================================
2025-07-10 18:04:38 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:04:38 - jira_api - INFO - 任务数量: 8
2025-07-10 18:04:38 - jira_api - INFO - 环境: test
2025-07-10 18:04:38 - jira_api - INFO - 项目: TEST
2025-07-10 18:04:38 - jira_api - INFO - 用户: test_user
2025-07-10 18:04:38 - jira_api - INFO - Sprint: INST2025-sprint10
2025-07-10 18:04:38 - jira_api - INFO - ================================================================================
2025-07-10 18:04:38 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:04:38 - jira_api - INFO - 发现主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-10 18:04:38 - jira_api - INFO - 发现主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-10 18:04:38 - jira_api - INFO - 任务结构分析完成: 2 个主任务, 8 个子任务
2025-07-10 18:04:38 - jira_api - INFO - 分析结果: 2 个主任务, 8 个子任务
2025-07-10 18:04:38 - jira_api - INFO - ============================================================
2025-07-10 18:04:38 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:04:38 - jira_api - INFO - 需要创建 2 个主任务
2025-07-10 18:04:38 - jira_api - INFO - ============================================================
2025-07-10 18:04:38 - jira_api - INFO - 创建主任务: MCP优化
2025-07-10 18:04:39 - jira_api - INFO - ================================================================================
2025-07-10 18:04:39 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:04:39 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 18:04:39 - jira_api - INFO - 请求方法: POST
2025-07-10 18:04:39 - jira_api - INFO - 认证用户: test_user
2025-07-10 18:04:39 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:04:39 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "MCP优化",
    "description": "橙卡: JGKEZH-23876\n模块: 《MCP追加第四、五大点》\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "test.qa"
    }
  }
}
2025-07-10 18:04:39 - jira_api - INFO - ================================================================================
2025-07-10 18:04:39 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:04:39 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:04:39 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:04:39 - jira_api - INFO - 响应体:
2025-07-10 18:04:39 - jira_api - INFO - 响应文本: 
2025-07-10 18:04:39 - jira_api - INFO - ================================================================================
2025-07-10 18:04:39 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:04:39 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:04:39 - jira_api - ERROR - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:04:39 - jira_api - INFO - 创建主任务: 用户权限优化
2025-07-10 18:04:39 - app.services.jira_service - WARNING - 未找到姓名 '张三' 对应的OA账号
2025-07-10 18:04:39 - jira_api - INFO - ================================================================================
2025-07-10 18:04:39 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:04:39 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/2/issue
2025-07-10 18:04:39 - jira_api - INFO - 请求方法: POST
2025-07-10 18:04:39 - jira_api - INFO - 认证用户: test_user
2025-07-10 18:04:39 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:04:39 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "TEST"
    },
    "summary": "用户权限优化",
    "description": "橙卡: JGKEZH-24150\n模块: 用户管理模块\n预估工作量: 2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "test_user"
    },
    "customfield_11305": {
      "name": "test.qa"
    }
  }
}
2025-07-10 18:04:39 - jira_api - INFO - ================================================================================
2025-07-10 18:04:40 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:04:40 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:04:40 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:04:40 - jira_api - INFO - 响应体:
2025-07-10 18:04:40 - jira_api - INFO - 响应文本: 
2025-07-10 18:04:40 - jira_api - INFO - ================================================================================
2025-07-10 18:04:40 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:04:40 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:04:40 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:04:40 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-10 18:04:40 - jira_api - INFO - ============================================================
2025-07-10 18:04:40 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:04:40 - jira_api - INFO - ============================================================
2025-07-10 18:04:40 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:04:40 - jira_api - INFO - ============================================================
2025-07-10 18:04:40 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:04:40 - jira_api - INFO - 需要创建 8 个子任务
2025-07-10 18:04:40 - jira_api - INFO - ============================================================
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 'web端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 'cms端 应用文案全部替换为fundx应用' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 'MCP优化测试' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 'CMS自动生成【MCP介绍】' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 'mcp服务可用工具列表接口' 的主任务 'MCP优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 '用户角色管理界面优化' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 '权限验证API接口' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - ERROR - 子任务 '权限功能测试' 的主任务 '用户权限优化' 未找到对应的JIRA Key
2025-07-10 18:04:40 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 8 个
2025-07-10 18:04:40 - jira_api - INFO - ================================================================================
2025-07-10 18:04:40 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:04:40 - jira_api - INFO - 总任务数: 8
2025-07-10 18:04:40 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:04:40 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:04:40 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:04:40 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:04:40 - jira_api - INFO - 成功: 0 个
2025-07-10 18:04:40 - jira_api - INFO - 失败: 10 个
2025-07-10 18:04:40 - jira_api - INFO - ================================================================================
2025-07-10 18:08:43 - root - INFO - 日志系统初始化完成
2025-07-10 18:08:43 - root - INFO - 日志级别: INFO
2025-07-10 18:08:43 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:08:43 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:08:56 - root - INFO - 日志系统初始化完成
2025-07-10 18:08:56 - root - INFO - 日志级别: INFO
2025-07-10 18:08:56 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:08:56 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:09:08 - root - INFO - 日志系统初始化完成
2025-07-10 18:09:08 - root - INFO - 日志级别: INFO
2025-07-10 18:09:08 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:09:08 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:09:21 - root - INFO - 日志系统初始化完成
2025-07-10 18:09:21 - root - INFO - 日志级别: INFO
2025-07-10 18:09:21 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:09:21 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:09:32 - root - INFO - 日志系统初始化完成
2025-07-10 18:09:32 - root - INFO - 日志级别: INFO
2025-07-10 18:09:32 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:09:32 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:09:44 - root - INFO - 日志系统初始化完成
2025-07-10 18:09:44 - root - INFO - 日志级别: INFO
2025-07-10 18:09:44 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:09:44 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:09:58 - root - INFO - 日志系统初始化完成
2025-07-10 18:09:58 - root - INFO - 日志级别: INFO
2025-07-10 18:09:58 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:09:58 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:10:09 - root - INFO - 日志系统初始化完成
2025-07-10 18:10:09 - root - INFO - 日志级别: INFO
2025-07-10 18:10:09 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:10:09 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:10:26 - root - INFO - 日志系统初始化完成
2025-07-10 18:10:26 - root - INFO - 日志级别: INFO
2025-07-10 18:10:26 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:10:26 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:10:43 - root - INFO - 日志系统初始化完成
2025-07-10 18:10:43 - root - INFO - 日志级别: INFO
2025-07-10 18:10:43 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:10:43 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:11:24 - root - INFO - 日志系统初始化完成
2025-07-10 18:11:24 - root - INFO - 日志级别: INFO
2025-07-10 18:11:24 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:11:24 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:11:56 - root - INFO - 日志系统初始化完成
2025-07-10 18:11:56 - root - INFO - 日志级别: INFO
2025-07-10 18:11:56 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:11:56 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:12:16 - root - INFO - 日志系统初始化完成
2025-07-10 18:12:16 - root - INFO - 日志级别: INFO
2025-07-10 18:12:16 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:12:16 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:13:18 - root - INFO - 日志系统初始化完成
2025-07-10 18:13:18 - root - INFO - 日志级别: INFO
2025-07-10 18:13:18 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:13:18 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:13:18 - jira_api - INFO - ================================================================================
2025-07-10 18:13:18 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:13:18 - jira_api - INFO - 任务数量: 8
2025-07-10 18:13:18 - jira_api - INFO - 环境: test
2025-07-10 18:13:18 - jira_api - INFO - 项目: JGKEZH
2025-07-10 18:13:18 - jira_api - INFO - 用户: lidezheng
2025-07-10 18:13:18 - jira_api - INFO - Sprint: INST2025-sprint10
2025-07-10 18:13:18 - jira_api - INFO - ================================================================================
2025-07-10 18:13:18 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:13:18 - jira_api - INFO - 发现主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-10 18:13:18 - jira_api - INFO - 发现主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-10 18:13:18 - jira_api - INFO - 任务结构分析完成: 2 个主任务, 8 个子任务
2025-07-10 18:13:18 - jira_api - INFO - 分析结果: 2 个主任务, 8 个子任务
2025-07-10 18:13:18 - jira_api - INFO - ============================================================
2025-07-10 18:13:18 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:13:18 - jira_api - INFO - 需要创建 2 个主任务
2025-07-10 18:13:18 - jira_api - INFO - ============================================================
2025-07-10 18:13:18 - jira_api - INFO - 创建主任务: MCP优化
2025-07-10 18:13:19 - jira_api - INFO - ================================================================================
2025-07-10 18:13:19 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:13:19 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:13:19 - jira_api - INFO - 请求方法: POST
2025-07-10 18:13:19 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:13:19 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:13:19 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "MCP优化",
    "description": "橙卡: JGKEZH-23876\n模块: 《MCP追加第四、五大点》\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:13:19 - jira_api - INFO - ================================================================================
2025-07-10 18:13:19 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:13:19 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:13:19 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:13:19 - jira_api - INFO - 响应体:
2025-07-10 18:13:19 - jira_api - INFO - 响应文本: 
2025-07-10 18:13:19 - jira_api - INFO - ================================================================================
2025-07-10 18:13:19 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:13:19 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:13:19 - jira_api - ERROR - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:13:19 - jira_api - INFO - 创建主任务: 用户权限优化
2025-07-10 18:13:20 - app.services.jira_service - WARNING - 未找到姓名 '张三' 对应的OA账号
2025-07-10 18:13:20 - jira_api - INFO - ================================================================================
2025-07-10 18:13:20 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:13:20 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:13:20 - jira_api - INFO - 请求方法: POST
2025-07-10 18:13:20 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:13:20 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:13:20 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "用户权限优化",
    "description": "橙卡: JGKEZH-24150\n模块: 用户管理模块\n预估工作量: 2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:13:20 - jira_api - INFO - ================================================================================
2025-07-10 18:13:20 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:13:20 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:13:20 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:13:20 - jira_api - INFO - 响应体:
2025-07-10 18:13:20 - jira_api - INFO - 响应文本: 
2025-07-10 18:13:20 - jira_api - INFO - ================================================================================
2025-07-10 18:13:20 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:13:20 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:13:20 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-10 18:13:20 - jira_api - INFO - ============================================================
2025-07-10 18:13:20 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:13:20 - jira_api - INFO - ============================================================
2025-07-10 18:13:20 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:13:20 - jira_api - INFO - ============================================================
2025-07-10 18:13:20 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:13:20 - jira_api - INFO - 需要创建 8 个子任务
2025-07-10 18:13:20 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-10 18:13:20 - jira_api - INFO - ============================================================
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'web端 应用文案全部替换为fundx应用' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'cms端 应用文案全部替换为fundx应用' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'MCP优化测试' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'CMS自动生成【MCP介绍】' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'mcp服务可用工具列表接口' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败，子任务 '用户角色管理界面优化' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败，子任务 '权限验证API接口' 无法创建
2025-07-10 18:13:20 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败，子任务 '权限功能测试' 无法创建
2025-07-10 18:13:20 - jira_api - INFO - 有效子任务: 0 个
2025-07-10 18:13:20 - jira_api - INFO - 因主任务失败而跳过的子任务: 8 个
2025-07-10 18:13:20 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 8 个
2025-07-10 18:13:20 - jira_api - INFO - ================================================================================
2025-07-10 18:13:20 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:13:20 - jira_api - INFO - 总任务数: 8
2025-07-10 18:13:20 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:13:20 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:13:20 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:13:20 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:13:20 - jira_api - INFO - 成功: 0 个
2025-07-10 18:13:20 - jira_api - INFO - 失败: 10 个
2025-07-10 18:13:20 - jira_api - INFO - ================================================================================
2025-07-10 18:14:43 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代', '代码分支', '任务依赖', '后端接口文档提供时间', '备注']
2025-07-10 18:14:43 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 18:14:43 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务             子任务 任务类型  负责人  工作量                 迭代  代码分支  任务依赖  后端接口文档提供时间  备注
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化    多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10   NaN   NaN         NaN NaN
1  JGKEZH-24332  三季度优化        CMS代办通知邮件      发送邮件模板通知调整   后端  黄翌晨  0.3  INST2025-sprint10   NaN   NaN         NaN NaN
2  JGKEZH-24332  三季度优化      ATP专业系统申请优化          提示文案更新   UI   关远  0.1  INST2025-sprint10   NaN   NaN         NaN NaN
3  JGKEZH-24332  三季度优化      ATP专业系统申请优化  需要开通的极速柜台，改为必填   UI   关远  0.2  INST2025-sprint10   NaN   NaN         NaN NaN
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请       cms审核支持退回   UI   关远  0.1  INST2025-sprint10   NaN   NaN         NaN NaN
2025-07-10 18:15:07 - root - INFO - ================================================================================
2025-07-10 18:15:07 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-10 18:15:07 - root - INFO - 任务数量: 6
2025-07-10 18:15:07 - root - INFO - 环境: test
2025-07-10 18:15:07 - root - INFO - 用户: lidezheng
2025-07-10 18:15:07 - root - INFO - 项目: JGKEZH
2025-07-10 18:15:07 - root - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:15:07 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-10 18:15:07 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-10 18:15:07 - root - INFO - 任务详情:
2025-07-10 18:15:07 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-10 18:15:07 - root - INFO -   任务 2: 发送邮件模板通知调整 - 负责人: 黄翌晨
2025-07-10 18:15:07 - root - INFO -   任务 3: 提示文案更新 - 负责人: 关远
2025-07-10 18:15:07 - root - INFO -   任务 4: 需要开通的极速柜台，改为必填 - 负责人: 关远
2025-07-10 18:15:07 - root - INFO -   任务 5: cms审核支持退回 - 负责人: 关远
2025-07-10 18:15:07 - root - INFO -   任务 6: 申请订单页-【申请期限】字段反显sku的配置值 - 负责人: 耿庆岩
2025-07-10 18:15:07 - root - INFO - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:15:07 - jira_api - INFO - 任务数量: 6
2025-07-10 18:15:07 - jira_api - INFO - 环境: test
2025-07-10 18:15:07 - jira_api - INFO - 项目: JGKEZH
2025-07-10 18:15:07 - jira_api - INFO - 用户: lidezheng
2025-07-10 18:15:07 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:15:07 - jira_api - INFO - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:15:07 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-10 18:15:07 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 18:15:07 - jira_api - INFO - 发现主任务: CMS代办通知邮件
2025-07-10 18:15:07 - jira_api - INFO - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-10 18:15:07 - jira_api - INFO - 发现主任务: ATP专业系统申请优化
2025-07-10 18:15:07 - jira_api - INFO - 发现子任务: 提示文案更新 -> 主任务: ATP专业系统申请优化
2025-07-10 18:15:07 - jira_api - INFO - 发现子任务: 需要开通的极速柜台，改为必填 -> 主任务: ATP专业系统申请优化
2025-07-10 18:15:07 - jira_api - INFO - 发现主任务: PB系统支持退回删除操作员申请
2025-07-10 18:15:07 - jira_api - INFO - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:15:07 - jira_api - INFO - 发现主任务: Web端「个人中心」
2025-07-10 18:15:07 - jira_api - INFO - 发现子任务: 申请订单页-【申请期限】字段反显sku的配置值 -> 主任务: Web端「个人中心」
2025-07-10 18:15:07 - jira_api - INFO - 任务结构分析完成: 5 个主任务, 6 个子任务
2025-07-10 18:15:07 - jira_api - INFO - 分析结果: 5 个主任务, 6 个子任务
2025-07-10 18:15:07 - jira_api - INFO - ============================================================
2025-07-10 18:15:07 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:15:07 - jira_api - INFO - 需要创建 5 个主任务
2025-07-10 18:15:07 - jira_api - INFO - ============================================================
2025-07-10 18:15:07 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-10 18:15:07 - jira_api - INFO - ================================================================================
2025-07-10 18:15:07 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:15:07 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:07 - jira_api - INFO - 请求方法: POST
2025-07-10 18:15:07 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:15:07 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:15:07 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:07 - jira_api - INFO - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:15:09 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:15:09 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:09 - jira_api - INFO - 响应体:
2025-07-10 18:15:09 - jira_api - INFO - 响应文本: 
2025-07-10 18:15:09 - jira_api - INFO - ================================================================================
2025-07-10 18:15:09 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - INFO - 创建主任务: CMS代办通知邮件
2025-07-10 18:15:09 - jira_api - INFO - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:15:09 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:09 - jira_api - INFO - 请求方法: POST
2025-07-10 18:15:09 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:15:09 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:15:09 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "wxhuangyichen"
    },
    "customfield_13103": {
      "name": "wxhuangyichen"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:09 - jira_api - INFO - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:15:09 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:15:09 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:09 - jira_api - INFO - 响应体:
2025-07-10 18:15:09 - jira_api - INFO - 响应文本: 
2025-07-10 18:15:09 - jira_api - INFO - ================================================================================
2025-07-10 18:15:09 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - ERROR - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:09 - jira_api - INFO - 创建主任务: ATP专业系统申请优化
2025-07-10 18:15:09 - jira_api - INFO - ================================================================================
2025-07-10 18:15:09 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:15:09 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:09 - jira_api - INFO - 请求方法: POST
2025-07-10 18:15:09 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:15:09 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:15:09 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "ATP专业系统申请优化",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:09 - jira_api - INFO - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:15:10 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:15:10 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:10 - jira_api - INFO - 响应体:
2025-07-10 18:15:10 - jira_api - INFO - 响应文本: 
2025-07-10 18:15:10 - jira_api - INFO - ================================================================================
2025-07-10 18:15:10 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - 主任务 'ATP专业系统申请优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - INFO - 创建主任务: PB系统支持退回删除操作员申请
2025-07-10 18:15:10 - jira_api - INFO - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:15:10 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:10 - jira_api - INFO - 请求方法: POST
2025-07-10 18:15:10 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:15:10 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:15:10 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:10 - jira_api - INFO - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:15:10 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:15:10 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:10 - jira_api - INFO - 响应体:
2025-07-10 18:15:10 - jira_api - INFO - 响应文本: 
2025-07-10 18:15:10 - jira_api - INFO - ================================================================================
2025-07-10 18:15:10 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:10 - jira_api - INFO - 创建主任务: Web端「个人中心」
2025-07-10 18:15:10 - app.services.jira_service - WARNING - 未找到姓名 '耿庆岩' 对应的OA账号
2025-07-10 18:15:10 - jira_api - INFO - ================================================================================
2025-07-10 18:15:10 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:15:10 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:15:10 - jira_api - INFO - 请求方法: POST
2025-07-10 18:15:10 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:15:10 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:15:10 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "Web端「个人中心」",
    "description": "橙卡: JGKEZH-23514\n模块: 开放广发专区优化\n预估工作量: 0.2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:15:10 - jira_api - INFO - ================================================================================
2025-07-10 18:15:16 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:15:16 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:15:16 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:15:16 - jira_api - INFO - 响应体:
2025-07-10 18:15:16 - jira_api - INFO - 响应文本: 
2025-07-10 18:15:16 - jira_api - INFO - ================================================================================
2025-07-10 18:15:16 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:15:16 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:15:16 - jira_api - ERROR - 主任务 'Web端「个人中心」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:15:16 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 5 个
2025-07-10 18:15:16 - jira_api - INFO - ============================================================
2025-07-10 18:15:16 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:15:16 - jira_api - INFO - ============================================================
2025-07-10 18:15:16 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:15:16 - jira_api - INFO - ============================================================
2025-07-10 18:15:16 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:15:16 - jira_api - INFO - 需要创建 6 个子任务
2025-07-10 18:15:16 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-10 18:15:16 - jira_api - INFO - ============================================================
2025-07-10 18:15:16 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - 主任务 'CMS代办通知邮件' 创建失败，子任务 '发送邮件模板通知调整' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - 主任务 'ATP专业系统申请优化' 创建失败，子任务 '提示文案更新' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - 主任务 'ATP专业系统申请优化' 创建失败，子任务 '需要开通的极速柜台，改为必填' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'cms审核支持退回' 无法创建
2025-07-10 18:15:16 - jira_api - ERROR - 主任务 'Web端「个人中心」' 创建失败，子任务 '申请订单页-【申请期限】字段反显sku的配置值' 无法创建
2025-07-10 18:15:16 - jira_api - INFO - 有效子任务: 0 个
2025-07-10 18:15:16 - jira_api - INFO - 因主任务失败而跳过的子任务: 6 个
2025-07-10 18:15:16 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 6 个
2025-07-10 18:15:16 - jira_api - INFO - ================================================================================
2025-07-10 18:15:16 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:15:16 - jira_api - INFO - 总任务数: 6
2025-07-10 18:15:16 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:15:16 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:15:16 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:15:16 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:15:16 - jira_api - INFO - 成功: 0 个
2025-07-10 18:15:16 - jira_api - INFO - 失败: 11 个
2025-07-10 18:15:16 - jira_api - INFO - ================================================================================
2025-07-10 18:15:16 - root - INFO - ================================================================================
2025-07-10 18:15:16 - root - INFO - JIRA批量任务创建结果:
2025-07-10 18:15:16 - root - INFO - 总任务数: 6
2025-07-10 18:15:16 - root - INFO - 主任务创建: 0 个
2025-07-10 18:15:16 - root - INFO - 子任务创建: 0 个
2025-07-10 18:15:16 - root - INFO - Sprint关联: 0 个
2025-07-10 18:15:16 - root - INFO - 需求关联: 0 个
2025-07-10 18:15:16 - root - INFO - 成功: 0 个
2025-07-10 18:15:16 - root - INFO - 失败: 11 个
2025-07-10 18:15:16 - root - INFO - 返回结果:
2025-07-10 18:15:16 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "type": "main_task",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "CMS代办通知邮件",
        "assignee": "黄翌晨",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "ATP专业系统申请优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "Web端「个人中心」",
        "assignee": "耿庆岩",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "多个场景设定图片上传比例",
        "main_task": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "发送邮件模板通知调整",
        "main_task": "CMS代办通知邮件",
        "assignee": "黄翌晨",
        "task_type": "后端",
        "error": "主任务 'CMS代办通知邮件' 创建失败，子任务 '发送邮件模板通知调整' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "提示文案更新",
        "main_task": "ATP专业系统申请优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'ATP专业系统申请优化' 创建失败，子任务 '提示文案更新' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "需要开通的极速柜台，改为必填",
        "main_task": "ATP专业系统申请优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'ATP专业系统申请优化' 创建失败，子任务 '需要开通的极速柜台，改为必填' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "cms审核支持退回",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'cms审核支持退回' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "申请订单页-【申请期限】字段反显sku的配置值",
        "main_task": "Web端「个人中心」",
        "assignee": "耿庆岩",
        "task_type": "UI",
        "error": "主任务 'Web端「个人中心」' 创建失败，子任务 '申请订单页-【申请期限】字段反显sku的配置值' 无法创建"
      }
    ],
    "summary": {
      "total": 6,
      "success_count": 0,
      "failed_count": 11,
      "main_tasks_created": 0,
      "sub_tasks_created": 0,
      "sprint_linked": 0,
      "demands_linked": 0
    }
  }
}
2025-07-10 18:15:16 - root - INFO - ================================================================================
2025-07-10 18:16:02 - root - INFO - Excel文件实际列名: ['公司基本面', '管理规模']
2025-07-10 18:17:42 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代']
2025-07-10 18:17:42 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 18:17:42 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务           子任务 任务类型  负责人  工作量                 迭代
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化  多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10
1  JGKEZH-24332  三季度优化        CMS代办通知邮件    发送邮件模板通知调整   后端  林文杰  0.3  INST2025-sprint10
2  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请     cms审核支持退回   UI   关远  0.1  INST2025-sprint10
3  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  web删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  CMS删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
2025-07-10 18:18:14 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代']
2025-07-10 18:18:14 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 18:18:14 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务           子任务 任务类型  负责人  工作量                 迭代
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化  多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10
1  JGKEZH-24332  三季度优化        CMS代办通知邮件    发送邮件模板通知调整   后端  林文杰  0.3  INST2025-sprint10
2  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请     cms审核支持退回   UI   关远  0.1  INST2025-sprint10
3  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  web删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  CMS删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
2025-07-10 18:18:29 - root - INFO - ================================================================================
2025-07-10 18:18:29 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-10 18:18:29 - root - INFO - 任务数量: 11
2025-07-10 18:18:29 - root - INFO - 环境: test
2025-07-10 18:18:29 - root - INFO - 用户: lidezheng
2025-07-10 18:18:29 - root - INFO - 项目: JGKEZH
2025-07-10 18:18:29 - root - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:18:29 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-10 18:18:29 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-10 18:18:29 - root - INFO - 任务详情:
2025-07-10 18:18:29 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-10 18:18:29 - root - INFO -   任务 2: 发送邮件模板通知调整 - 负责人: 林文杰
2025-07-10 18:18:29 - root - INFO -   任务 3: cms审核支持退回 - 负责人: 关远
2025-07-10 18:18:29 - root - INFO -   任务 4: web删除操作员逻辑调整 - 负责人: 林文杰
2025-07-10 18:18:29 - root - INFO -   任务 5: CMS删除操作员逻辑调整 - 负责人: 林文杰
2025-07-10 18:18:29 - root - INFO -   任务 6: 产品目录树列表和筛选 - 负责人: 关远
2025-07-10 18:18:29 - root - INFO -   任务 7: 产品管理新增【关联产品目录树】选项 - 负责人: 关远
2025-07-10 18:18:29 - root - INFO -   任务 8: 产品目录树列表 - 负责人: 林文杰
2025-07-10 18:18:29 - root - INFO -   任务 9: 目录新增&编辑 - 负责人: 林文杰
2025-07-10 18:18:29 - root - INFO -   任务 10: 筛选项新增 类型 - 负责人: 关远
2025-07-10 18:18:29 - root - INFO -   任务 11: 开通列表支持类型筛选 - 负责人: 林文杰
2025-07-10 18:18:29 - root - INFO - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:18:29 - jira_api - INFO - 任务数量: 11
2025-07-10 18:18:29 - jira_api - INFO - 环境: test
2025-07-10 18:18:29 - jira_api - INFO - 项目: JGKEZH
2025-07-10 18:18:29 - jira_api - INFO - 用户: lidezheng
2025-07-10 18:18:29 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:18:29 - jira_api - INFO - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:18:29 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 18:18:29 - jira_api - INFO - 发现主任务: CMS代办通知邮件
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-10 18:18:29 - jira_api - INFO - 发现主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:29 - jira_api - INFO - 发现主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-10 18:18:29 - jira_api - INFO - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:29 - jira_api - INFO - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:29 - jira_api - INFO - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-10 18:18:29 - jira_api - INFO - 分析结果: 5 个主任务, 11 个子任务
2025-07-10 18:18:29 - jira_api - INFO - ============================================================
2025-07-10 18:18:29 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:18:29 - jira_api - INFO - 需要创建 5 个主任务
2025-07-10 18:18:29 - jira_api - INFO - ============================================================
2025-07-10 18:18:29 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-10 18:18:29 - jira_api - INFO - ================================================================================
2025-07-10 18:18:29 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:18:29 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:29 - jira_api - INFO - 请求方法: POST
2025-07-10 18:18:29 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:18:29 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:18:29 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:29 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:18:30 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:18:30 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:30 - jira_api - INFO - 响应体:
2025-07-10 18:18:30 - jira_api - INFO - 响应文本: 
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - INFO - 创建主任务: CMS代办通知邮件
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:18:30 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:30 - jira_api - INFO - 请求方法: POST
2025-07-10 18:18:30 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:18:30 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:18:30 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:18:30 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:18:30 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:30 - jira_api - INFO - 响应体:
2025-07-10 18:18:30 - jira_api - INFO - 响应文本: 
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - 主任务 'CMS代办通知邮件' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - INFO - 创建主任务: PB系统支持退回删除操作员申请
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:18:30 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:30 - jira_api - INFO - 请求方法: POST
2025-07-10 18:18:30 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:18:30 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:18:30 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.1d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:18:30 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:18:30 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:30 - jira_api - INFO - 响应体:
2025-07-10 18:18:30 - jira_api - INFO - 响应文本: 
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:30 - jira_api - INFO - 创建主任务: CMS端「产品目录树」
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:30 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:18:30 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:30 - jira_api - INFO - 请求方法: POST
2025-07-10 18:18:30 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:18:30 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:18:30 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "description": "橙卡: JGKEZH-2410\n模块: 三季度优化\n预估工作量: 1.0d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:30 - jira_api - INFO - ================================================================================
2025-07-10 18:18:31 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:18:31 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:18:31 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:31 - jira_api - INFO - 响应体:
2025-07-10 18:18:31 - jira_api - INFO - 响应文本: 
2025-07-10 18:18:31 - jira_api - INFO - ================================================================================
2025-07-10 18:18:31 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS端「产品目录树」' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - INFO - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-10 18:18:31 - jira_api - INFO - ================================================================================
2025-07-10 18:18:31 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:18:31 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:18:31 - jira_api - INFO - 请求方法: POST
2025-07-10 18:18:31 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:18:31 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:18:31 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "description": "橙卡: JGKEZH-2410\n模块: 三季度优化\n预估工作量: 0.2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:18:31 - jira_api - INFO - ================================================================================
2025-07-10 18:18:31 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:18:31 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:18:31 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:18:31 - jira_api - INFO - 响应体:
2025-07-10 18:18:31 - jira_api - INFO - 响应文本: 
2025-07-10 18:18:31 - jira_api - INFO - ================================================================================
2025-07-10 18:18:31 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS「产品管理-开通列表」优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:18:31 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 5 个
2025-07-10 18:18:31 - jira_api - INFO - ============================================================
2025-07-10 18:18:31 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:18:31 - jira_api - INFO - ============================================================
2025-07-10 18:18:31 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:18:31 - jira_api - INFO - ============================================================
2025-07-10 18:18:31 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:18:31 - jira_api - INFO - 需要创建 11 个子任务
2025-07-10 18:18:31 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-10 18:18:31 - jira_api - INFO - ============================================================
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS代办通知邮件' 创建失败，子任务 '发送邮件模板通知调整' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'cms审核支持退回' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'web删除操作员逻辑调整' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'CMS删除操作员逻辑调整' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表和筛选' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品管理新增【关联产品目录树】选项' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS端「产品目录树」' 创建失败，子任务 '目录新增&编辑' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '筛选项新增 类型' 无法创建
2025-07-10 18:18:31 - jira_api - ERROR - 主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '开通列表支持类型筛选' 无法创建
2025-07-10 18:18:31 - jira_api - INFO - 有效子任务: 0 个
2025-07-10 18:18:31 - jira_api - INFO - 因主任务失败而跳过的子任务: 11 个
2025-07-10 18:18:31 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 11 个
2025-07-10 18:18:31 - jira_api - INFO - ================================================================================
2025-07-10 18:18:31 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:18:31 - jira_api - INFO - 总任务数: 11
2025-07-10 18:18:31 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:18:31 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:18:31 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:18:31 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:18:31 - jira_api - INFO - 成功: 0 个
2025-07-10 18:18:31 - jira_api - INFO - 失败: 16 个
2025-07-10 18:18:31 - jira_api - INFO - ================================================================================
2025-07-10 18:18:31 - root - INFO - ================================================================================
2025-07-10 18:18:31 - root - INFO - JIRA批量任务创建结果:
2025-07-10 18:18:31 - root - INFO - 总任务数: 11
2025-07-10 18:18:31 - root - INFO - 主任务创建: 0 个
2025-07-10 18:18:31 - root - INFO - 子任务创建: 0 个
2025-07-10 18:18:31 - root - INFO - Sprint关联: 0 个
2025-07-10 18:18:31 - root - INFO - 需求关联: 0 个
2025-07-10 18:18:31 - root - INFO - 成功: 0 个
2025-07-10 18:18:31 - root - INFO - 失败: 16 个
2025-07-10 18:18:31 - root - INFO - 返回结果:
2025-07-10 18:18:31 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "type": "main_task",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "CMS代办通知邮件",
        "assignee": "林文杰",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "CMS端「产品目录树」",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "main_task",
        "title": "CMS「产品管理-开通列表」优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "多个场景设定图片上传比例",
        "main_task": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "发送邮件模板通知调整",
        "main_task": "CMS代办通知邮件",
        "assignee": "林文杰",
        "task_type": "后端",
        "error": "主任务 'CMS代办通知邮件' 创建失败，子任务 '发送邮件模板通知调整' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "cms审核支持退回",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'cms审核支持退回' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "web删除操作员逻辑调整",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "林文杰",
        "task_type": "API",
        "error": "主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'web删除操作员逻辑调整' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "CMS删除操作员逻辑调整",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "林文杰",
        "task_type": "API",
        "error": "主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'CMS删除操作员逻辑调整' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "产品目录树列表和筛选",
        "main_task": "CMS端「产品目录树」",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表和筛选' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "产品管理新增【关联产品目录树】选项",
        "main_task": "CMS端「产品目录树」",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品管理新增【关联产品目录树】选项' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "产品目录树列表",
        "main_task": "CMS端「产品目录树」",
        "assignee": "林文杰",
        "task_type": "API",
        "error": "主任务 'CMS端「产品目录树」' 创建失败，子任务 '产品目录树列表' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "目录新增&编辑",
        "main_task": "CMS端「产品目录树」",
        "assignee": "林文杰",
        "task_type": "API",
        "error": "主任务 'CMS端「产品目录树」' 创建失败，子任务 '目录新增&编辑' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "筛选项新增 类型",
        "main_task": "CMS「产品管理-开通列表」优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '筛选项新增 类型' 无法创建"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "开通列表支持类型筛选",
        "main_task": "CMS「产品管理-开通列表」优化",
        "assignee": "林文杰",
        "task_type": "API",
        "error": "主任务 'CMS「产品管理-开通列表」优化' 创建失败，子任务 '开通列表支持类型筛选' 无法创建"
      }
    ],
    "summary": {
      "total": 11,
      "success_count": 0,
      "failed_count": 16,
      "main_tasks_created": 0,
      "sub_tasks_created": 0,
      "sprint_linked": 0,
      "demands_linked": 0
    }
  }
}
2025-07-10 18:18:31 - root - INFO - ================================================================================
2025-07-10 18:20:19 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代']
2025-07-10 18:20:19 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 18:20:19 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务           子任务 任务类型  负责人  工作量                 迭代
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化  多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10
1  JGKEZH-24332  三季度优化        CMS代办通知邮件    发送邮件模板通知调整   后端  林文杰  0.3  INST2025-sprint10
2  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请     cms审核支持退回   UI   关远  0.1  INST2025-sprint10
3  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  web删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  CMS删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
2025-07-10 18:20:44 - root - INFO - 日志系统初始化完成
2025-07-10 18:20:44 - root - INFO - 日志级别: INFO
2025-07-10 18:20:44 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:20:44 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:21:15 - root - INFO - 日志系统初始化完成
2025-07-10 18:21:15 - root - INFO - 日志级别: INFO
2025-07-10 18:21:15 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:21:15 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:21:42 - root - INFO - 日志系统初始化完成
2025-07-10 18:21:42 - root - INFO - 日志级别: INFO
2025-07-10 18:21:42 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:21:42 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:22:06 - root - INFO - 日志系统初始化完成
2025-07-10 18:22:06 - root - INFO - 日志级别: INFO
2025-07-10 18:22:06 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:22:06 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:22:31 - root - INFO - 日志系统初始化完成
2025-07-10 18:22:31 - root - INFO - 日志级别: INFO
2025-07-10 18:22:31 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:22:31 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:22:53 - root - INFO - 日志系统初始化完成
2025-07-10 18:22:53 - root - INFO - 日志级别: INFO
2025-07-10 18:22:53 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:22:53 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:23:09 - root - INFO - 日志系统初始化完成
2025-07-10 18:23:09 - root - INFO - 日志级别: INFO
2025-07-10 18:23:09 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:23:09 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:23:09 - jira_api - INFO - ================================================================================
2025-07-10 18:23:09 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:23:09 - jira_api - INFO - 任务数量: 8
2025-07-10 18:23:09 - jira_api - INFO - 环境: test
2025-07-10 18:23:09 - jira_api - INFO - 项目: JGKEZH
2025-07-10 18:23:09 - jira_api - INFO - 用户: lidezheng
2025-07-10 18:23:09 - jira_api - INFO - Sprint: INST2025-sprint10
2025-07-10 18:23:09 - jira_api - INFO - ================================================================================
2025-07-10 18:23:09 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:23:09 - jira_api - INFO - 发现主任务: MCP优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: web端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: cms端 应用文案全部替换为fundx应用 -> 主任务: MCP优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: MCP优化测试 -> 主任务: MCP优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: CMS自动生成【MCP介绍】 -> 主任务: MCP优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: mcp服务可用工具列表接口 -> 主任务: MCP优化
2025-07-10 18:23:09 - jira_api - INFO - 发现主任务: 用户权限优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: 用户角色管理界面优化 -> 主任务: 用户权限优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: 权限验证API接口 -> 主任务: 用户权限优化
2025-07-10 18:23:09 - jira_api - INFO - 发现子任务: 权限功能测试 -> 主任务: 用户权限优化
2025-07-10 18:23:09 - jira_api - INFO - 任务结构分析完成: 2 个主任务, 8 个子任务
2025-07-10 18:23:09 - jira_api - INFO - 分析结果: 2 个主任务, 8 个子任务
2025-07-10 18:23:09 - jira_api - INFO - ============================================================
2025-07-10 18:23:09 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:23:09 - jira_api - INFO - 需要创建 2 个主任务
2025-07-10 18:23:09 - jira_api - INFO - ============================================================
2025-07-10 18:23:09 - jira_api - INFO - 创建主任务: MCP优化
2025-07-10 18:23:10 - jira_api - INFO - ================================================================================
2025-07-10 18:23:10 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:23:10 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:23:10 - jira_api - INFO - 请求方法: POST
2025-07-10 18:23:10 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:23:10 - jira_api - INFO - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-10 18:23:10 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:23:10 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "MCP优化",
    "description": "橙卡: JGKEZH-23876\n模块: 《MCP追加第四、五大点》\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:23:10 - jira_api - INFO - ================================================================================
2025-07-10 18:23:10 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:23:10 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:23:10 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:23:10 - jira_api - INFO - 响应体:
2025-07-10 18:23:10 - jira_api - INFO - 响应文本: 
2025-07-10 18:23:10 - jira_api - INFO - ================================================================================
2025-07-10 18:23:10 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:23:10 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:23:10 - jira_api - ERROR - 主任务 'MCP优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:23:10 - jira_api - INFO - 创建主任务: 用户权限优化
2025-07-10 18:23:10 - app.services.jira_service - WARNING - 未找到姓名 '张三' 对应的OA账号
2025-07-10 18:23:10 - jira_api - INFO - ================================================================================
2025-07-10 18:23:10 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:23:10 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:23:10 - jira_api - INFO - 请求方法: POST
2025-07-10 18:23:10 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:23:10 - jira_api - INFO - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-10 18:23:10 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:23:10 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "用户权限优化",
    "description": "橙卡: JGKEZH-24150\n模块: 用户管理模块\n预估工作量: 2d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:23:10 - jira_api - INFO - ================================================================================
2025-07-10 18:23:11 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:23:11 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:23:11 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:23:11 - jira_api - INFO - 响应体:
2025-07-10 18:23:11 - jira_api - INFO - 响应文本: 
2025-07-10 18:23:11 - jira_api - INFO - ================================================================================
2025-07-10 18:23:11 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:23:11 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:23:11 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 2 个
2025-07-10 18:23:11 - jira_api - INFO - ============================================================
2025-07-10 18:23:11 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:23:11 - jira_api - INFO - ============================================================
2025-07-10 18:23:11 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:23:11 - jira_api - INFO - ============================================================
2025-07-10 18:23:11 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:23:11 - jira_api - INFO - 需要创建 8 个子任务
2025-07-10 18:23:11 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-10 18:23:11 - jira_api - INFO - ============================================================
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'web端 应用文案全部替换为fundx应用' 无法创建
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'cms端 应用文案全部替换为fundx应用' 无法创建
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'MCP优化测试' 无法创建
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'CMS自动生成【MCP介绍】' 无法创建
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 'MCP优化' 创建失败，子任务 'mcp服务可用工具列表接口' 无法创建
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败，子任务 '用户角色管理界面优化' 无法创建
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败，子任务 '权限验证API接口' 无法创建
2025-07-10 18:23:11 - jira_api - ERROR - 主任务 '用户权限优化' 创建失败，子任务 '权限功能测试' 无法创建
2025-07-10 18:23:11 - jira_api - INFO - 有效子任务: 0 个
2025-07-10 18:23:11 - jira_api - INFO - 因主任务失败而跳过的子任务: 8 个
2025-07-10 18:23:11 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 8 个
2025-07-10 18:23:11 - jira_api - INFO - ================================================================================
2025-07-10 18:23:11 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:23:11 - jira_api - INFO - 总任务数: 8
2025-07-10 18:23:11 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:23:11 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:23:11 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:23:11 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:23:11 - jira_api - INFO - 成功: 0 个
2025-07-10 18:23:11 - jira_api - INFO - 失败: 10 个
2025-07-10 18:23:11 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - root - INFO - ================================================================================
2025-07-10 18:25:59 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-10 18:25:59 - root - INFO - 任务数量: 1
2025-07-10 18:25:59 - root - INFO - 环境: test
2025-07-10 18:25:59 - root - INFO - 用户: lidezheng
2025-07-10 18:25:59 - root - INFO - 项目: JGKEZH
2025-07-10 18:25:59 - root - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:25:59 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-10 18:25:59 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-10 18:25:59 - root - INFO - 任务详情:
2025-07-10 18:25:59 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-10 18:25:59 - root - INFO - ================================================================================
2025-07-10 18:25:59 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:25:59 - jira_api - INFO - 任务数量: 1
2025-07-10 18:25:59 - jira_api - INFO - 环境: test
2025-07-10 18:25:59 - jira_api - INFO - 项目: JGKEZH
2025-07-10 18:25:59 - jira_api - INFO - 用户: lidezheng
2025-07-10 18:25:59 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:25:59 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:25:59 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-10 18:25:59 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 18:25:59 - jira_api - INFO - 任务结构分析完成: 1 个主任务, 1 个子任务
2025-07-10 18:25:59 - jira_api - INFO - 分析结果: 1 个主任务, 1 个子任务
2025-07-10 18:25:59 - jira_api - INFO - ============================================================
2025-07-10 18:25:59 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:25:59 - jira_api - INFO - 需要创建 1 个主任务
2025-07-10 18:25:59 - jira_api - INFO - ============================================================
2025-07-10 18:25:59 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-10 18:25:59 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:25:59 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:25:59 - jira_api - INFO - 请求方法: POST
2025-07-10 18:25:59 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:25:59 - jira_api - INFO - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-10 18:25:59 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:25:59 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "description": "橙卡: JGKEZH-24332\n模块: 三季度优化\n预估工作量: 0.3d",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:25:59 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:25:59 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:25:59 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:25:59 - jira_api - INFO - 响应体:
2025-07-10 18:25:59 - jira_api - INFO - 响应文本: 
2025-07-10 18:25:59 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:25:59 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:25:59 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:25:59 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 1 个
2025-07-10 18:25:59 - jira_api - INFO - ============================================================
2025-07-10 18:25:59 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:25:59 - jira_api - INFO - ============================================================
2025-07-10 18:25:59 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:25:59 - jira_api - INFO - ============================================================
2025-07-10 18:25:59 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:25:59 - jira_api - INFO - 需要创建 1 个子任务
2025-07-10 18:25:59 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-10 18:25:59 - jira_api - INFO - ============================================================
2025-07-10 18:25:59 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-10 18:25:59 - jira_api - INFO - 有效子任务: 0 个
2025-07-10 18:25:59 - jira_api - INFO - 因主任务失败而跳过的子任务: 1 个
2025-07-10 18:25:59 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 1 个
2025-07-10 18:25:59 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:25:59 - jira_api - INFO - 总任务数: 1
2025-07-10 18:25:59 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:25:59 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:25:59 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:25:59 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:25:59 - jira_api - INFO - 成功: 0 个
2025-07-10 18:25:59 - jira_api - INFO - 失败: 2 个
2025-07-10 18:25:59 - jira_api - INFO - ================================================================================
2025-07-10 18:25:59 - root - INFO - ================================================================================
2025-07-10 18:25:59 - root - INFO - JIRA批量任务创建结果:
2025-07-10 18:25:59 - root - INFO - 总任务数: 1
2025-07-10 18:25:59 - root - INFO - 主任务创建: 0 个
2025-07-10 18:25:59 - root - INFO - 子任务创建: 0 个
2025-07-10 18:25:59 - root - INFO - Sprint关联: 0 个
2025-07-10 18:25:59 - root - INFO - 需求关联: 0 个
2025-07-10 18:25:59 - root - INFO - 成功: 0 个
2025-07-10 18:25:59 - root - INFO - 失败: 2 个
2025-07-10 18:25:59 - root - INFO - 返回结果:
2025-07-10 18:25:59 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "type": "main_task",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "多个场景设定图片上传比例",
        "main_task": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建"
      }
    ],
    "summary": {
      "total": 1,
      "success_count": 0,
      "failed_count": 2,
      "main_tasks_created": 0,
      "sub_tasks_created": 0,
      "sprint_linked": 0,
      "demands_linked": 0
    }
  }
}
2025-07-10 18:25:59 - root - INFO - ================================================================================
2025-07-10 18:27:20 - root - INFO - 日志系统初始化完成
2025-07-10 18:27:20 - root - INFO - 日志级别: INFO
2025-07-10 18:27:20 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:27:20 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:27:30 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代']
2025-07-10 18:27:30 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 18:27:30 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务           子任务 任务类型  负责人  工作量                 迭代
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化  多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10
1  JGKEZH-24332  三季度优化        CMS代办通知邮件    发送邮件模板通知调整   后端  林文杰  0.3  INST2025-sprint10
2  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请     cms审核支持退回   UI   关远  0.1  INST2025-sprint10
3  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  web删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  CMS删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
2025-07-10 18:27:40 - root - INFO - ================================================================================
2025-07-10 18:27:40 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-10 18:27:40 - root - INFO - 任务数量: 1
2025-07-10 18:27:40 - root - INFO - 环境: test
2025-07-10 18:27:40 - root - INFO - 用户: lidezheng
2025-07-10 18:27:40 - root - INFO - 项目: JGKEZH
2025-07-10 18:27:40 - root - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:27:40 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-10 18:27:40 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-10 18:27:40 - root - INFO - 任务详情:
2025-07-10 18:27:40 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-10 18:27:40 - root - INFO - ================================================================================
2025-07-10 18:27:40 - jira_api - INFO - ================================================================================
2025-07-10 18:27:40 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:27:40 - jira_api - INFO - 任务数量: 1
2025-07-10 18:27:40 - jira_api - INFO - 环境: test
2025-07-10 18:27:40 - jira_api - INFO - 项目: JGKEZH
2025-07-10 18:27:40 - jira_api - INFO - 用户: lidezheng
2025-07-10 18:27:40 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:27:40 - jira_api - INFO - ================================================================================
2025-07-10 18:27:40 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:27:40 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-10 18:27:40 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 18:27:40 - jira_api - INFO - 任务结构分析完成: 1 个主任务, 1 个子任务
2025-07-10 18:27:40 - jira_api - INFO - 分析结果: 1 个主任务, 1 个子任务
2025-07-10 18:27:40 - jira_api - INFO - ============================================================
2025-07-10 18:27:40 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:27:40 - jira_api - INFO - 需要创建 1 个主任务
2025-07-10 18:27:40 - jira_api - INFO - ============================================================
2025-07-10 18:27:40 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-10 18:27:40 - jira_api - INFO - ================================================================================
2025-07-10 18:27:40 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:27:40 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:27:40 - jira_api - INFO - 请求方法: POST
2025-07-10 18:27:40 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:27:40 - jira_api - INFO - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-10 18:27:40 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:27:40 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:27:40 - jira_api - INFO - ================================================================================
2025-07-10 18:27:40 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:27:40 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:27:40 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:27:40 - jira_api - INFO - 响应体:
2025-07-10 18:27:40 - jira_api - INFO - 响应文本: 
2025-07-10 18:27:40 - jira_api - INFO - ================================================================================
2025-07-10 18:27:40 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:27:40 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:27:40 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:27:40 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 1 个
2025-07-10 18:27:40 - jira_api - INFO - ============================================================
2025-07-10 18:27:40 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:27:40 - jira_api - INFO - ============================================================
2025-07-10 18:27:40 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:27:40 - jira_api - INFO - ============================================================
2025-07-10 18:27:40 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:27:40 - jira_api - INFO - 需要创建 1 个子任务
2025-07-10 18:27:40 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-10 18:27:40 - jira_api - INFO - ============================================================
2025-07-10 18:27:40 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-10 18:27:40 - jira_api - INFO - 有效子任务: 0 个
2025-07-10 18:27:40 - jira_api - INFO - 因主任务失败而跳过的子任务: 1 个
2025-07-10 18:27:40 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 1 个
2025-07-10 18:27:40 - jira_api - INFO - ================================================================================
2025-07-10 18:27:40 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:27:40 - jira_api - INFO - 总任务数: 1
2025-07-10 18:27:40 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:27:40 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:27:40 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:27:40 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:27:40 - jira_api - INFO - 成功: 0 个
2025-07-10 18:27:40 - jira_api - INFO - 失败: 2 个
2025-07-10 18:27:40 - jira_api - INFO - ================================================================================
2025-07-10 18:27:40 - root - INFO - ================================================================================
2025-07-10 18:27:40 - root - INFO - JIRA批量任务创建结果:
2025-07-10 18:27:40 - root - INFO - 总任务数: 1
2025-07-10 18:27:40 - root - INFO - 主任务创建: 0 个
2025-07-10 18:27:40 - root - INFO - 子任务创建: 0 个
2025-07-10 18:27:40 - root - INFO - Sprint关联: 0 个
2025-07-10 18:27:40 - root - INFO - 需求关联: 0 个
2025-07-10 18:27:40 - root - INFO - 成功: 0 个
2025-07-10 18:27:40 - root - INFO - 失败: 2 个
2025-07-10 18:27:40 - root - INFO - 返回结果:
2025-07-10 18:27:40 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "type": "main_task",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "多个场景设定图片上传比例",
        "main_task": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建"
      }
    ],
    "summary": {
      "total": 1,
      "success_count": 0,
      "failed_count": 2,
      "main_tasks_created": 0,
      "sub_tasks_created": 0,
      "sprint_linked": 0,
      "demands_linked": 0
    }
  }
}
2025-07-10 18:27:40 - root - INFO - ================================================================================
2025-07-10 18:29:23 - root - INFO - 日志系统初始化完成
2025-07-10 18:29:23 - root - INFO - 日志级别: INFO
2025-07-10 18:29:23 - root - INFO - 应用日志文件: logs/app_20250710.log
2025-07-10 18:29:23 - root - INFO - JIRA API日志文件: logs/jira_api_20250710.log
2025-07-10 18:29:35 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量', '迭代']
2025-07-10 18:29:35 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-10 18:29:35 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务           子任务 任务类型  负责人  工作量                 迭代
0  JGKEZH-24332  三季度优化      CMS图片裁剪功能优化  多个场景设定图片上传比例   UI   关远  0.3  INST2025-sprint10
1  JGKEZH-24332  三季度优化        CMS代办通知邮件    发送邮件模板通知调整   后端  林文杰  0.3  INST2025-sprint10
2  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请     cms审核支持退回   UI   关远  0.1  INST2025-sprint10
3  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  web删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
4  JGKEZH-24332  三季度优化  PB系统支持退回删除操作员申请  CMS删除操作员逻辑调整  API  林文杰  1.0  INST2025-sprint10
2025-07-10 18:29:42 - root - INFO - ================================================================================
2025-07-10 18:29:42 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-10 18:29:42 - root - INFO - 任务数量: 1
2025-07-10 18:29:42 - root - INFO - 环境: test
2025-07-10 18:29:42 - root - INFO - 用户: lidezheng
2025-07-10 18:29:42 - root - INFO - 项目: JGKEZH
2025-07-10 18:29:42 - root - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:29:42 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-10 18:29:42 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-10 18:29:42 - root - INFO - 任务详情:
2025-07-10 18:29:42 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-10 18:29:42 - root - INFO - ================================================================================
2025-07-10 18:29:42 - jira_api - INFO - ================================================================================
2025-07-10 18:29:42 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-10 18:29:42 - jira_api - INFO - 任务数量: 1
2025-07-10 18:29:42 - jira_api - INFO - 环境: test
2025-07-10 18:29:42 - jira_api - INFO - 项目: JGKEZH
2025-07-10 18:29:42 - jira_api - INFO - 用户: lidezheng
2025-07-10 18:29:42 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-10 18:29:42 - jira_api - INFO - ================================================================================
2025-07-10 18:29:42 - jira_api - INFO - 开始分析任务层次结构...
2025-07-10 18:29:42 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-10 18:29:42 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-10 18:29:42 - jira_api - INFO - 任务结构分析完成: 1 个主任务, 1 个子任务
2025-07-10 18:29:42 - jira_api - INFO - 分析结果: 1 个主任务, 1 个子任务
2025-07-10 18:29:42 - jira_api - INFO - ============================================================
2025-07-10 18:29:42 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-10 18:29:42 - jira_api - INFO - 需要创建 1 个主任务
2025-07-10 18:29:42 - jira_api - INFO - ============================================================
2025-07-10 18:29:42 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-10 18:29:42 - jira_api - INFO - ================================================================================
2025-07-10 18:29:42 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-10 18:29:42 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-10 18:29:42 - jira_api - INFO - 请求方法: POST
2025-07-10 18:29:42 - jira_api - INFO - 认证用户: lidezheng
2025-07-10 18:29:42 - jira_api - INFO - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json'}
2025-07-10 18:29:42 - jira_api - INFO - 请求体 (JSON):
2025-07-10 18:29:42 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-10 18:29:42 - jira_api - INFO - ================================================================================
2025-07-10 18:29:43 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-10 18:29:43 - jira_api - INFO - 响应状态码: 502
2025-07-10 18:29:43 - jira_api - INFO - 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-07-10 18:29:43 - jira_api - INFO - 响应体:
2025-07-10 18:29:43 - jira_api - INFO - 响应文本: 
2025-07-10 18:29:43 - jira_api - INFO - ================================================================================
2025-07-10 18:29:43 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 502 - 
2025-07-10 18:29:43 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 502 - 
2025-07-10 18:29:43 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败: 创建主任务失败: JIRA API错误: 502 - 
2025-07-10 18:29:43 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 1 个
2025-07-10 18:29:43 - jira_api - INFO - ============================================================
2025-07-10 18:29:43 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-10 18:29:43 - jira_api - INFO - ============================================================
2025-07-10 18:29:43 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-10 18:29:43 - jira_api - INFO - ============================================================
2025-07-10 18:29:43 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-10 18:29:43 - jira_api - INFO - 需要创建 1 个子任务
2025-07-10 18:29:43 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-10 18:29:43 - jira_api - INFO - ============================================================
2025-07-10 18:29:43 - jira_api - ERROR - 主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建
2025-07-10 18:29:43 - jira_api - INFO - 有效子任务: 0 个
2025-07-10 18:29:43 - jira_api - INFO - 因主任务失败而跳过的子任务: 1 个
2025-07-10 18:29:43 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 1 个
2025-07-10 18:29:43 - jira_api - INFO - ================================================================================
2025-07-10 18:29:43 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-10 18:29:43 - jira_api - INFO - 总任务数: 1
2025-07-10 18:29:43 - jira_api - INFO - 主任务创建: 0 个
2025-07-10 18:29:43 - jira_api - INFO - 子任务创建: 0 个
2025-07-10 18:29:43 - jira_api - INFO - Sprint关联: 0 个
2025-07-10 18:29:43 - jira_api - INFO - 需求关联: 0 个
2025-07-10 18:29:43 - jira_api - INFO - 成功: 0 个
2025-07-10 18:29:43 - jira_api - INFO - 失败: 2 个
2025-07-10 18:29:43 - jira_api - INFO - ================================================================================
2025-07-10 18:29:43 - root - INFO - ================================================================================
2025-07-10 18:29:43 - root - INFO - JIRA批量任务创建结果:
2025-07-10 18:29:43 - root - INFO - 总任务数: 1
2025-07-10 18:29:43 - root - INFO - 主任务创建: 0 个
2025-07-10 18:29:43 - root - INFO - 子任务创建: 0 个
2025-07-10 18:29:43 - root - INFO - Sprint关联: 0 个
2025-07-10 18:29:43 - root - INFO - 需求关联: 0 个
2025-07-10 18:29:43 - root - INFO - 成功: 0 个
2025-07-10 18:29:43 - root - INFO - 失败: 2 个
2025-07-10 18:29:43 - root - INFO - 返回结果:
2025-07-10 18:29:43 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "type": "main_task",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "error": "创建主任务失败: JIRA API错误: 502 - "
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "多个场景设定图片上传比例",
        "main_task": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "task_type": "UI",
        "error": "主任务 'CMS图片裁剪功能优化' 创建失败，子任务 '多个场景设定图片上传比例' 无法创建"
      }
    ],
    "summary": {
      "total": 1,
      "success_count": 0,
      "failed_count": 2,
      "main_tasks_created": 0,
      "sub_tasks_created": 0,
      "sprint_linked": 0,
      "demands_linked": 0
    }
  }
}
2025-07-10 18:29:43 - root - INFO - ================================================================================
